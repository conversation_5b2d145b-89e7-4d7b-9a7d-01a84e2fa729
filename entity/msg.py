from marshmallow import Schema, fields

from typing import Optional, List, Dict
from entity.NLPContext import NLPContext
from entity.NLPContextSchema import NLPContextSchema


class Msg:
    def __init__(self, query: Optional[str] = None, context: Optional[NLPContext] = None, traceId: Optional[str] = None):
        self.query = query or ""               # 用户输入语句
        self.context = context or NLPContext() # 上下文对象
        self.traceId = traceId or ""           # 追踪 ID


class MsgSchema(Schema):
    query = fields.String(required=True)
    traceId = fields.String(required=True)
    context = fields.Nested(lambda: NLPContextSchema(), required=True)

    class Meta:
        model = Msg