# config.py

class Config:
    # 基础配置
    space = []
    devices = []
    DEVICE_LIST = []
    SCENE_LIST = []
    WEATHER_INDEX = []
    LIVING = []

    # 模型配置
    model = ""
    model_server = ""
    api_key = ""
    mode_type = "ds"  # 默认模式

    # 地址和设备信息
    home_address = ""
    device_names = []

    # 日志配置
    log_dir = "./log"

    # 本地微调模型配置
    use_local_model = False
    local_base_model_path = ""
    local_finetuned_model_path = ""
    local_model_device = "auto"

    # 多GPU配置
    local_gpu_devices = "6,7"  # 默认使用6号和7号GPU
    local_conda_env = "/opt/conda/FT"  # 虚拟环境路径

    @classmethod
    def reset_to_defaults(cls):
        """重置配置为默认值"""
        cls.space = []
        cls.devices = []
        cls.DEVICE_LIST = []
        cls.SCENE_LIST = []
        cls.WEATHER_INDEX = []
        cls.LIVING = []
        cls.model = ""
        cls.model_server = ""
        cls.api_key = ""
        cls.mode_type = "ds"
        cls.home_address = ""
        cls.device_names = []
        cls.log_dir = "./log"

        # 重置本地模型配置
        cls.use_local_model = False
        cls.local_base_model_path = ""
        cls.local_finetuned_model_path = ""
        cls.local_model_device = "auto"
        cls.local_gpu_devices = "6,7"
        cls.local_conda_env = "/opt/conda/FT"
