from marshmallow import Schema, fields

from entity.candidate import Candidate
from entity.NLPContext import NLPContext


class CandidateSchema(Schema):
    domains = fields.List(fields.Str(), required=False, allow_none=True)
    devices = fields.List(fields.Str(), required=False, allow_none=True)

    class Meta:
        model = Candidate


class NLPContextSchema(Schema):
    homeGraph = fields.Dict(required=False, allow_none=True)  # JSONObject → dict
    traceId = fields.Str(required=False, allow_none=True)
    scenes = fields.List(fields.Str(), required=False, allow_none=True)
    rooms = fields.List(fields.Str(), required=False, allow_none=True)
    candidate = fields.Nested(CandidateSchema, required=False, allow_none=True)
    missing = fields.Str(required=False, allow_none=True)
    devices = fields.List(fields.Dict(required=False, allow_none=True), required=False, allow_none=True)
    class Meta:
        model = NLPContext