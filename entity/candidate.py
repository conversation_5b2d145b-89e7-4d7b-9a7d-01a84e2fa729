from typing import Optional, List

class Candidate:
    def __init__(self, domains: Optional[List[str]] = None, devices: Optional[List[str]] = None):
        self.domains = domains or []
        self.devices = devices or []

    @staticmethod
    def new_device_candidate(devices: Optional[List[str]] = None):
        """
        静态工厂方法，用于创建仅包含 devices 的 Candidate 对象
        """
        return Candidate(devices=devices)