from typing import Optional, List, Dict
from json import JSONDecoder  
from entity.candidate import Candidate


class NLPContext:
    def __init__(
        self,
        homeGraph: Optional[Dict] = None,  # 替代 JSONObject
        traceId: Optional[str] = None,
        scenes: Optional[List[str]] = None,
        rooms: Optional[List[str]] = None,
        candidate: Optional[Candidate] = None,
        missing: Optional[str] = None
    ):
        self.homeGraph = homeGraph or {}
        self.traceId = traceId or ""
        self.scenes = scenes or []
        self.rooms = rooms or []
        self.candidate = candidate or Candidate()
        self.missing = missing or ""