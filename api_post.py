#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
替代Postman进行接口测试
"""

import requests
import json
import time
from datetime import datetime

def test_send_msg_api():
    """测试/send_msg接口"""
    
    # API配置
    base_url = "http://127.0.0.1:8578"
    endpoint = "/send_msg"
    url = base_url + endpoint
    
    # 请求数据
    payload = {
    "query": "帮我把卧室的灯调高亮度，再打开客厅的窗帘，并启动回家模式",
    "traceId": "abc123",
    "context": {
        "scenes": [
            "测试专用",
            "会客模式",
            "离家模式",
            "睡眠模式",
            "用餐模式"
        ],
        "rooms": [
            "客厅",
            "主卧",
            "客卧",
            "次卧",
            "书房",
            "卫生间",
            "餐厅",
            "厨房",
            "阳台",
            "走廊",
            "玄关",
            "儿童房",
            "过道",
            "全屋",
            "海上"
        ],
        "devices": [
            # 网关和安防设备
            {
                "name": "Zero3",
                "domain": "网关",
                "space_name": "客厅"
            },
            {
                "name": "Zero3",
                "domain": "安防",
                "space_name": "客厅"
            },
            {
                "name": "Zero5",
                "domain": "网关",
                "space_name": "客厅"
            },
            {
                "name": "Zero5",
                "domain": "安防",
                "space_name": "客厅"
            },
            {
                "name": "Zero 3S(零火)",
                "domain": "安防",
                "space_name": "客厅"
            },
            # 主卧灯具设备
            {
                "name": "筒灯一",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "筒灯",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "明装筒灯",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "明装筒灯一",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "吸顶灯一",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "氛围灯",
                "domain": "灯",
                "space_name": "主卧"
            },
            # 全屋灯具设备
            {
                "name": "全屋明装筒灯",
                "domain": "灯",
                "space_name": "全屋"
            },
            {
                "name": "全屋灯",
                "domain": "灯",
                "space_name": "全屋"
            },
            # 过道设备
            {
                "name": "过道三开",
                "domain": "开关",
                "space_name": "过道"
            },
            # 通断器设备
            {
                "name": "一路通断器",
                "domain": "开关",
                "space_name": "主卧"
            },
            {
                "name": "通断器",
                "domain": "开关",
                "space_name": "主卧"
            },
            {
                "name": "通断器一",
                "domain": "开关",
                "space_name": "主卧"
            },
            {
                "name": "6路通断器灯一",
                "domain": "开关",
                "space_name": "主卧"
            },
            {
                "name": "全屋通断器",
                "domain": "开关",
                "space_name": "全屋"
            },
            # 海上设备（特殊测试场景）
            {
                "name": "海上的灯",
                "domain": "灯",
                "space_name": "海上"
            },
            # 窗帘设备
            {
                "name": "纱帘",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "布帘",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "窗帘电机",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "窗帘",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "全屋窗帘",
                "domain": "窗帘",
                "space_name": "全屋"
            },
            {
                "name": "窗帘面板一",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "窗帘面板",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "窗帘一",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "全屋窗帘面板",
                "domain": "窗帘",
                "space_name": "全屋"
            },
            # 空调设备
            {
                "name": "空调一",
                "domain": "空调",
                "space_name": "主卧"
            },
            {
                "name": "空调",
                "domain": "空调",
                "space_name": "主卧"
            },
            {
                "name": "主卧空调",
                "domain": "空调",
                "space_name": "主卧"
            },
            {
                "name": "客厅东空调",
                "domain": "空调",
                "space_name": "客厅"
            },
            {
                "name": "客厅西空调",
                "domain": "空调",
                "space_name": "客厅"
            },
            {
                "name": "客厅空调",
                "domain": "空调",
                "space_name": "客厅"
            },
            {
                "name": "全家空调",
                "domain": "空调",
                "space_name": "全屋"
            },
            # 开关设备
            {
                "name": "开关一",
                "domain": "开关",
                "space_name": "次卧"
            },
            {
                "name": "开关",
                "domain": "开关",
                "space_name": "次卧"
            },
            {
                "name": "过道三开",
                "domain": "开关",
                "space_name": "客厅"
            },
            {
                "name": "餐厅筒灯",
                "domain": "灯",
                "space_name": "客厅"
            },
            {
                "name": "筒灯",
                "domain": "灯",
                "space_name": "客厅"
            },
            {
                "name": "客厅灯",
                "domain": "灯",
                "space_name": "客厅"
            },
            {
                "name": "全家开关",
                "domain": "开关",
                "space_name": "全屋"
            },
            {
                "name": "主卧双开",
                "domain": "开关",
                "space_name": "主卧"
            },
            {
                "name": "开关三",
                "domain": "开关",
                "space_name": "书房"
            },
            # 插座设备
            {
                "name": "插座一",
                "domain": "插座",
                "space_name": "主卧"
            },
            {
                "name": "插座",
                "domain": "插座",
                "space_name": "主卧"
            },
            {
                "name": "全家插座",
                "domain": "插座",
                "space_name": "全屋"
            },
            # 地暖设备
            {
                "name": "地暖一",
                "domain": "地暖",
                "space_name": "主卧"
            },
            {
                "name": "地暖",
                "domain": "地暖",
                "space_name": "主卧"
            },
            {
                "name": "全家地暖",
                "domain": "地暖",
                "space_name": "全屋"
            },
            # 新风设备
            {
                "name": "新风",
                "domain": "新风机",
                "space_name": "主卧"
            },
            {
                "name": "新风一",
                "domain": "新风机",
                "space_name": "主卧"
            },
            {
                "name": "新风机",
                "domain": "新风机",
                "space_name": "主卧"
            },
            {
                "name": "全家新风机",
                "domain": "新风机",
                "space_name": "全屋"
            },
            # 厨房设备
            {
                "name": "油烟机",
                "domain": "油烟机",
                "space_name": "厨房"
            },
            {
                "name": "电饭煲",
                "domain": "电饭煲",
                "space_name": "厨房"
            },
            {
                "name": "微波炉",
                "domain": "微波炉",
                "space_name": "厨房"
            },
            # 洗衣设备
            {
                "name": "洗衣机",
                "domain": "洗衣机",
                "space_name": "阳台"
            },
            # 其他设备
            {
                "name": "按摩椅",
                "domain": "按摩椅",
                "space_name": "客厅"
            },
            {
                "name": "加湿器",
                "domain": "加湿器",
                "space_name": "客厅"
            },
            {
                "name": "投影",
                "domain": "投影仪",
                "space_name": "客厅"
            },
            {
                "name": "智能门锁 S3",
                "domain": "门锁",
                "space_name": "门锁"
            },
            {
                "name": "Free无线一键智能面板",
                "domain": "情景面板",
                "space_name": "Free随心贴"
            },
            {
                "name": "Free无线三键智能面板",
                "domain": "情景面板",
                "space_name": "Free随心贴"
            },
            {
                "name": "K5旋钮随意贴",
                "domain": "开关",
                "space_name": "Free随心贴"
            },
            {
                "name": "2号Zero2",
                "domain": "开关",
                "space_name": "主卧"
            },
            {
                "name": "灯1",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "灯2",
                "domain": "灯",
                "space_name": "主卧"
            },
            {
                "name": "Hope皓月三路开关面板",
                "domain": "开关",
                "space_name": "皓月"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "皓月"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "皓月"
            },
            {
                "name": "3 灯",
                "domain": "灯",
                "space_name": "皓月"
            },
            {
                "name": "皓月四路情景面板",
                "domain": "情景面板",
                "space_name": "皓月"
            },
            {
                "name": "Hope皓月多路调光面板",
                "domain": "灯",
                "space_name": "皓月"
            },
            {
                "name": "Hope皓月二路开关面板",
                "domain": "开关",
                "space_name": "皓月"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "皓月"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "皓月"
            },
            {
                "name": "Hope皓月一路窗帘面板",
                "domain": "窗帘",
                "space_name": "皓月"
            },
            {
                "name": "Hope皓月二路窗帘面板",
                "domain": "窗帘",
                "space_name": "皓月"
            },
            {
                "name": "K5旋钮随意贴",
                "domain": "开关",
                "space_name": "Free随心贴"
            },
            {
                "name": "K5旋钮屏",
                "domain": "开关",
                "space_name": "Free随心贴"
            },
            {
                "name": "开关1",
                "domain": "灯",
                "space_name": "Free随心贴"
            },
            {
                "name": "开关2",
                "domain": "灯",
                "space_name": "Free随心贴"
            },
            {
                "name": "智慧屏P1",
                "domain": "开关",
                "space_name": "次卧"
            },
            {
                "name": "聚光灯",
                "domain": "灯",
                "space_name": "次卧"
            },
            {
                "name": "苹果灯",
                "domain": "灯",
                "space_name": "次卧"
            },
            {
                "name": "香蕉灯",
                "domain": "灯",
                "space_name": "次卧"
            },
            {
                "name": "智慧屏P1",
                "domain": "网关",
                "space_name": "次卧"
            },
            {
                "name": "地暖通断器(ZigBee)",
                "domain": "加热器",
                "space_name": "地暖通断器"
            },
            {
                "name": "地暖1",
                "domain": "地暖",
                "space_name": "地暖通断器"
            },
            {
                "name": "地暖2",
                "domain": "地暖",
                "space_name": "地暖通断器"
            },
            {
                "name": "地暖3",
                "domain": "地暖",
                "space_name": "地暖通断器"
            },
            {
                "name": "地暖4",
                "domain": "地暖",
                "space_name": "地暖通断器"
            },
            {
                "name": "地暖5",
                "domain": "地暖",
                "space_name": "地暖通断器"
            },
            {
                "name": "地暖6",
                "domain": "地暖",
                "space_name": "地暖通断器"
            },
            {
                "name": "KL圆月一路开关(蓝牙)",
                "domain": "开关",
                "space_name": "书房"
            },
            {
                "name": "灯",
                "domain": "灯",
                "space_name": "书房"
            },
            {
                "name": "KL圆月双开四景(蓝牙)",
                "domain": "开关",
                "space_name": "书房"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "书房"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "书房"
            },
            {
                "name": "KL圆月二路开关(蓝牙)",
                "domain": "开关",
                "space_name": "书房"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "书房"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "书房"
            },
            {
                "name": "KL圆月一路窗帘(蓝牙)",
                "domain": "窗帘",
                "space_name": "书房"
            },
            {
                "name": "布帘(蓝牙)",
                "domain": "窗帘",
                "space_name": "客厅"
            },
            {
                "name": "空调室外机网关1",
                "domain": "智能控制器",
                "space_name": "空调新风"
            },
            {
                "name": "空调1",
                "domain": "空调",
                "space_name": "客厅"
            },
            {
                "name": "空调2",
                "domain": "空调",
                "space_name": "主卧"
            },
            {
                "name": "空调3",
                "domain": "空调",
                "space_name": "次卧"
            },
            {
                "name": "空调4",
                "domain": "空调",
                "space_name": "书房"
            },
            {
                "name": "空调5",
                "domain": "空调",
                "space_name": "空调新风"
            },
            {
                "name": "Free无线二键智能面板",
                "domain": "情景面板",
                "space_name": "Free随心贴"
            },
            {
                "name": "Free无线四键智能面板",
                "domain": "情景面板",
                "space_name": "Free随心贴"
            },
            {
                "name": "WE瓦力SOS紧急按钮",
                "domain": "警报器 ",
                "space_name": "瓦力"
            },
            {
                "name": "WE瓦力SOS紧急按钮C",
                "domain": "警报器 ",
                "space_name": "瓦力C"
            },
            {
                "name": "WE瓦力系列温湿度传感器C",
                "domain": "温湿度传感器",
                "space_name": "瓦力C"
            },
            {
                "name": "WE瓦力温湿度传感器",
                "domain": "温湿度传感器",
                "space_name": "瓦力"
            },
            {
                "name": "WE瓦力系列独立式光电感烟火灾探测报警器",
                "domain": "烟雾传感器",
                "space_name": "瓦力C"
            },
            {
                "name": "空调室内机网关",
                "domain": "空调",
                "space_name": "空调新风"
            },
            {
                "name": "3号Zero2",
                "domain": "开关",
                "space_name": "次卧"
            },
            {
                "name": "灯控1",
                "domain": "灯",
                "space_name": "次卧"
            },
            {
                "name": "灯控2",
                "domain": "灯",
                "space_name": "次卧"
            },
            {
                "name": "灯控3",
                "domain": "灯",
                "space_name": "次卧"
            },
            {
                "name": "3号Zero2",
                "domain": "网关",
                "space_name": "次卧"
            },
            {
                "name": "3号Zero2",
                "domain": "安防",
                "space_name": "次卧"
            },
            {
                "name": "4号Zero2",
                "domain": "开关",
                "space_name": "客厅"
            },
            {
                "name": "灯控1",
                "domain": "灯",
                "space_name": "客厅"
            },
            {
                "name": "灯控2",
                "domain": "灯",
                "space_name": "客厅"
            },
            {
                "name": "灯控3",
                "domain": "灯",
                "space_name": "客厅"
            },
            {
                "name": "4号Zero2",
                "domain": "网关",
                "space_name": "客厅"
            },
            {
                "name": "4号Zero2",
                "domain": "安防",
                "space_name": "客厅"
            },
            {
                "name": "JL智能开合窗帘ZY1(ZigBee)",
                "domain": "窗帘",
                "space_name": "书房"
            },
            {
                "name": "KL圆月 三路开关面板（ZigBee）",
                "domain": "开关",
                "space_name": "餐厅"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "3 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "KL圆月 二路开关面板（ZigBee）",
                "domain": "开关",
                "space_name": "餐厅"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "主卧纱帘",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "主卧布帘",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "零洞智慧门Dora7",
                "domain": "安防",
                "space_name": "书房"
            },
            {
                "name": "Aura欧拉嵌入式筒灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "Aura欧拉嵌入式筒灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "欧拉明装筒灯2",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "智慧门Dora3",
                "domain": "门锁",
                "space_name": "门锁"
            },
            {
                "name": "零洞红外遥控精灵KZ1（WiFi版）",
                "domain": "智能控制器",
                "space_name": "客厅"
            },
            {
                "name": "美的空调",
                "domain": "空调",
                "space_name": "客厅"
            },
            {
                "name": "智慧中控屏Zero 3S",
                "domain": "安防",
                "space_name": "客厅"
            },
            {
                "name": "KL圆月 三路开关面板（ZigBee）",
                "domain": "开关",
                "space_name": "餐厅"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "3 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "KL圆月 一路窗帘面板（ZigBee）",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "KL圆月 二路开关面板（ZigBee）",
                "domain": "开关",
                "space_name": "餐厅"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "KC新月一路窗帘面板(蓝牙)",
                "domain": "窗帘",
                "space_name": "餐厅"
            },
            {
                "name": "KC新月双开四景面板(蓝牙)",
                "domain": "开关",
                "space_name": "餐厅"
            },
            {
                "name": "111111111 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "22222222 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "KC新月四开双景面板(蓝牙)",
                "domain": "开关",
                "space_name": "餐厅"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "3 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "4 灯",
                "domain": "灯",
                "space_name": "餐厅"
            },
            {
                "name": "空调室外机网关2",
                "domain": "智能控制器",
                "space_name": "餐厅"
            },
            {
                "name": "空调11",
                "domain": "空调",
                "space_name": "餐厅"
            },
            {
                "name": "空调12",
                "domain": "空调",
                "space_name": "餐厅"
            },
            {
                "name": "空调13",
                "domain": "空调",
                "space_name": "餐厅"
            },
            {
                "name": "空调14",
                "domain": "空调",
                "space_name": "餐厅"
            },
            {
                "name": "Hope皓月三合一温控器",
                "domain": "温控器",
                "space_name": "餐厅"
            },
            {
                "name": "4 灯",
                "domain": "灯",
                "space_name": "k7p蓝牙"
            },
            {
                "name": "KC新月双开四景面板(蓝牙)",
                "domain": "开关",
                "space_name": "k7p蓝牙"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "k7p蓝牙"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "k7p蓝牙"
            },
            {
                "name": "KC新月四路情景面板(蓝牙)",
                "domain": "情景面板",
                "space_name": "k7p蓝牙"
            },
            {
                "name": "KC新月二路开关面板(ZigBee)",
                "domain": "开关",
                "space_name": "k7p新zigbee"
            },
            {
                "name": "1 灯",
                "domain": "灯",
                "space_name": "k7p新zigbee"
            },
            {
                "name": "2 灯",
                "domain": "灯",
                "space_name": "k7p新zigbee"
            },
            {
                "name": "Hope皓月16A智能插座",
                "domain": "插座",
                "space_name": "皓月"
            },
            {
                "name": "Hope皓月10A智能插座",
                "domain": "插座",
                "space_name": "皓月"
            },
            {
                "name": "JL智能开合窗帘ZY1(蓝牙)",
                "domain": "窗帘",
                "space_name": "主卧"
            },
            {
                "name": "JL智能开合窗帘ZY1(Zigbee)-B",
                "domain": "窗帘",
                "space_name": "主卧"
            }
        ],
        "candidate": {
            "domains": [],
            "devices": []
        },
        "homeGraph": {
            "name": "我的家庭",
            "id": "1283",
            "position": {
                "adName": "",
                "cityName": "",
                "pName": "",
                "cityCode": "440600",
                "latitude": "22.928231957573892",
                "adCode": "440606102",
                "pCode": "440000",
                "name": "广东省佛山市顺德区北滘镇",
                "longitude": "113.20699596198394",
                "typeCode": ""
            }
        }
    }
}
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=" * 80)
    print(f"🧪 API测试开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    print(f"📡 请求URL: {url}")
    print(f"📝 请求方法: POST")
    print(f"📋 请求体:")
    print(json.dumps(payload, ensure_ascii=False, indent=2))
    print("-" * 80)
    
    # 初始化响应时间变量
    request_time = None

    try:
        # 记录开始时间
        start_time = time.time()

        # 发送请求
        print("🚀 发送请求中...")
        response = requests.post(url, json=payload, headers=headers, timeout=120)

        # 记录结束时间
        end_time = time.time()
        request_time = end_time - start_time
        
        # 显示响应信息
        print(f"⏱️  请求耗时: {request_time:.2f}秒")
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        print("-" * 80)
        
        # 解析响应
        if response.status_code == 200:
            try:
                response_data = response.json()
                print("✅ 请求成功!")
                print("📄 响应内容:")
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
                
                # 分析响应结果
                if response_data.get("code") == "SUCCESS":
                    print("\n🎉 API调用成功!")
                    data = response_data.get("data", {})
                    if "inference_time" in data:
                        print(f"🤖 模型推理时间: {data['inference_time']:.2f}秒")
                    if "response" in data:
                        print(f"💬 模型响应: {data['response']}")
                else:
                    print(f"\n❌ API返回错误: {response_data.get('msg', '未知错误')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
                
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时 (120秒)")
        if 'start_time' in locals():
            request_time = time.time() - start_time
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请检查服务是否启动")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        if 'start_time' in locals():
            request_time = time.time() - start_time
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        if 'start_time' in locals():
            request_time = time.time() - start_time

    print("=" * 80)
    print("🏁 测试完成")
    if request_time is not None:
        print(f"📊 总响应时间: {request_time:.2f}秒")
    else:
        print("📊 无法计算响应时间（请求未完成）")
    print("=" * 80)

if __name__ == "__main__":
    test_send_msg_api()
