"""
统一配置管理器
负责整个项目的配置加载、验证和管理
"""

import os
import json
import logging
from typing import Optional, Dict, Any
from dotenv import load_dotenv
from entity.config import Config


class ConfigManager:
    """统一配置管理器 - 单例模式"""
    
    _instance: Optional['ConfigManager'] = None
    _initialized: bool = False
    
    def __new__(cls) -> 'ConfigManager':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.logger = logging.getLogger(__name__)
            self.project_root = self._get_project_root()
            self.env_path = os.path.join(self.project_root, '.env')
            self._initialized = True
    
    def _get_project_root(self) -> str:
        """获取项目根目录（app.py所在目录）"""
        current_file = os.path.abspath(__file__)
        # 从 config/config_manager.py 回到项目根目录
        return os.path.dirname(os.path.dirname(current_file))
    
    def _read_env_file_directly(self) -> dict:
        """直接读取.env文件，绕过缓存"""
        env_vars = {}
        try:
            with open(self.env_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        # 移除引号
                        value = value.strip().strip('"').strip("'")
                        env_vars[key.strip()] = value
        except Exception as e:
            self.logger.error(f"直接读取.env文件失败: {e}")
        return env_vars

    def load_config(self, force_reload: bool = False) -> bool:
        """
        加载配置

        Args:
            force_reload: 是否强制重新加载

        Returns:
            bool: 加载是否成功
        """
        try:
            # 检查.env文件是否存在
            if not os.path.exists(self.env_path):
                self.logger.error(f".env 文件不存在于路径: {self.env_path}")
                return False

            # 直接读取.env文件内容，绕过所有缓存
            env_vars = self._read_env_file_directly()

            # 重置配置为默认值
            Config.reset_to_defaults()

            # 直接从文件内容加载配置
            self._load_config_from_dict(env_vars)

            # 验证配置
            if not self._validate_config():
                return False

            self.logger.info(f"配置加载成功 - 模式: {Config.mode_type}, 模型: {Config.model}")
            return True

        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            return False
    
    def _load_config_from_dict(self, env_vars: dict):
        """从字典加载配置"""
        # 加载基础配置
        Config.mode_type = env_vars.get('MODE_TYPE', 'ds')

        # 加载JSON格式的配置
        try:
            Config.WEATHER_INDEX = json.loads(env_vars.get('WEATHER_INDEX', '[]'))
            Config.LIVING = json.loads(env_vars.get('LIVING', '[]'))
            Config.DEVICE_LIST = json.loads(env_vars.get('DEVICE_LIST', '[]'))
        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON配置解析失败: {e}")
            Config.WEATHER_INDEX = []
            Config.LIVING = []
            Config.DEVICE_LIST = []

        # 根据模式类型加载对应的模型配置
        if Config.mode_type == 'hw':
            Config.model = env_vars.get('HW_MODE', '')
            Config.model_server = env_vars.get('HW_MODE_SERVER', '')
            Config.api_key = env_vars.get('HW_API_KEY', '')
        elif Config.mode_type == 'qw':
            Config.model = env_vars.get('QW_MODE', '')
            Config.model_server = env_vars.get('QW_MODE_SERVER', '')
            Config.api_key = env_vars.get('QW_API_KEY', '')
        else:  # ds 或其他自定义模式
            Config.model = env_vars.get('MODE', '')
            Config.model_server = env_vars.get('MODE_SERVER', '')
            Config.api_key = env_vars.get('API_KEY', '')

        # 加载其他配置
        Config.log_dir = env_vars.get('LOG_DIR', './log')

        # 确保日志目录存在
        if not os.path.exists(Config.log_dir):
            os.makedirs(Config.log_dir)

        # 加载本地模型配置
        Config.use_local_model = env_vars.get('USE_LOCAL_MODEL', 'false').lower() == 'true'
        Config.local_base_model_path = env_vars.get('LOCAL_BASE_MODEL_PATH', '')
        Config.local_finetuned_model_path = env_vars.get('LOCAL_FINETUNED_MODEL_PATH', '')
        Config.local_model_device = env_vars.get('LOCAL_MODEL_DEVICE', 'auto')

        # 加载多GPU配置
        Config.local_gpu_devices = env_vars.get('LOCAL_GPU_DEVICES', '6,7')
        Config.local_conda_env = env_vars.get('LOCAL_CONDA_ENV', '/opt/conda/FT')

    def _load_basic_config(self):
        """加载基础配置（保留兼容性）"""
        Config.mode_type = os.getenv('MODE_TYPE', 'ds')

        # 加载JSON格式的配置
        try:
            Config.WEATHER_INDEX = json.loads(os.getenv('WEATHER_INDEX', '[]'))
            Config.LIVING = json.loads(os.getenv('LIVING', '[]'))
            Config.DEVICE_LIST = json.loads(os.getenv('DEVICE_LIST', '[]'))
        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON配置解析失败: {e}")
            Config.WEATHER_INDEX = []
            Config.LIVING = []
            Config.DEVICE_LIST = []
    
    def _load_model_config(self):
        """根据模式类型加载对应的模型配置"""
        if Config.mode_type == 'hw':
            Config.model = os.getenv('HW_MODE', '')
            Config.model_server = os.getenv('HW_MODE_SERVER', '')
            Config.api_key = os.getenv('HW_API_KEY', '')
        elif Config.mode_type == 'qw':
            Config.model = os.getenv('QW_MODE', '')
            Config.model_server = os.getenv('QW_MODE_SERVER', '')
            Config.api_key = os.getenv('QW_API_KEY', '')
        else:  # ds 或其他自定义模式
            Config.model = os.getenv('MODE', '')
            Config.model_server = os.getenv('MODE_SERVER', '')
            Config.api_key = os.getenv('API_KEY', '')
    
    def _load_additional_config(self):
        """加载其他配置"""
        # 日志配置
        Config.log_dir = os.getenv('LOG_DIR', './log')
        
        # 确保日志目录存在
        if not os.path.exists(Config.log_dir):
            os.makedirs(Config.log_dir)
    
    def _validate_config(self) -> bool:
        """验证配置的有效性"""
        required_fields = ['mode_type']
        
        for field in required_fields:
            if not getattr(Config, field, None):
                self.logger.error(f"必需的配置项 {field} 未设置")
                return False
        
        # 验证模型配置
        if Config.use_local_model:
            # 验证本地模型配置
            if not Config.local_base_model_path:
                self.logger.error("启用本地模型时必须设置 LOCAL_BASE_MODEL_PATH")
                return False
            if not Config.local_finetuned_model_path:
                self.logger.error("启用本地模型时必须设置 LOCAL_FINETUNED_MODEL_PATH")
                return False
            if not os.path.exists(Config.local_base_model_path):
                self.logger.error(f"本地基础模型路径不存在: {Config.local_base_model_path}")
                return False
            if not os.path.exists(Config.local_finetuned_model_path):
                self.logger.error(f"本地微调模型路径不存在: {Config.local_finetuned_model_path}")
                return False
        else:
            # 验证云端模型配置
            if Config.mode_type in ['hw', 'qw'] and not Config.api_key:
                self.logger.error(f"模式 {Config.mode_type} 需要设置 API_KEY")
                return False

        return True
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取当前配置信息"""
        config_info = {
            'mode_type': Config.mode_type,
            'model': Config.model,
            'model_server': Config.model_server,
            'api_key_set': bool(Config.api_key),
            'log_dir': getattr(Config, 'log_dir', './log'),
            'device_count': len(Config.devices),
            'scene_count': len(Config.SCENE_LIST),
            'room_count': len(Config.space),
            # 本地模型配置信息
            'use_local_model': getattr(Config, 'use_local_model', False),
            'local_base_model_path': getattr(Config, 'local_base_model_path', ''),
            'local_finetuned_model_path': getattr(Config, 'local_finetuned_model_path', ''),
            'local_model_device': getattr(Config, 'local_model_device', 'auto'),
            'local_gpu_devices': getattr(Config, 'local_gpu_devices', '6,7'),
            'local_conda_env': getattr(Config, 'local_conda_env', '/opt/conda/FT')
        }
        return config_info
    
    def reload_config(self) -> bool:
        """重新加载配置"""
        return self.load_config(force_reload=True)


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config_manager() -> ConfigManager:
    """获取配置管理器实例"""
    return config_manager


def load_config(force_reload: bool = False) -> bool:
    """便捷的配置加载函数"""
    return config_manager.load_config(force_reload)


def get_config_info() -> Dict[str, Any]:
    """便捷的配置信息获取函数"""
    return config_manager.get_config_info()
