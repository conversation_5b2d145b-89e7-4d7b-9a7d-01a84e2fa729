# 智能家居项目配置管理

## 概述

本项目采用统一的配置管理系统，通过单例模式的 `ConfigManager` 类来管理所有配置项。

## 配置文件

### 主配置文件：`.env`

项目根目录下的 `.env` 文件包含所有配置项：

```env
# 模型配置
MODE_TYPE=hw                    # 模型类型：hw(华为), qw(通义千问), ds(DeepSeek)

# 华为模型配置
HW_MODE=DeepSeek-V3
HW_MODE_SERVER=https://maas-cn-southwest-2.modelarts-maas.com/v1/infers/271c9332-4aa6-4ff5-95b3-0cf8bd94c394/v1
HW_API_KEY=your_hw_api_key

# 通义千问配置
QW_MODE=qwen-turbo-latest
QW_MODE_SERVER=https://dashscope.aliyuncs.com/compatible-mode/v1
QW_API_KEY=your_qw_api_key

# DeepSeek配置
MODE=/data/model/Qwen3-14B
MODE_SERVER=https://openai.linkdora.com/qwen3/v1
API_KEY=your_ds_api_key

# 功能配置
WEATHER_INDEX=["查询空气质量&空气污染扩散指数","查询空气湿度","查询温度/体感温度","查询风速/风向","查询天气状况","查询日出/日落时间"]
LIVING=["查询限行","查询化妆指数","查询紫外线指数","查询感冒指数","查询洗车指数","查询穿衣指数","查询运动指数","查询钓鱼指数"]
DEVICE_LIST=["插座","通断器","灯","开关","窗帘","空调","新风","地暖","安防"]

# 日志配置
LOG_DIR=./log
```

## 使用方法

### 1. 基本使用

```python
from config import load_config, get_config_info
from entity.config import Config

# 加载配置
if load_config():
    print("配置加载成功")
    
    # 使用配置
    print(f"当前模型类型: {Config.mode_type}")
    print(f"当前模型: {Config.model}")
    
    # 获取配置信息
    info = get_config_info()
    print(f"配置信息: {info}")
```

### 2. 高级使用

```python
from config import get_config_manager

# 获取配置管理器实例
manager = get_config_manager()

# 强制重新加载配置
manager.reload_config()

# 获取详细配置信息
info = manager.get_config_info()
```

## 配置管理器特性

### 单例模式
- `ConfigManager` 采用单例模式，确保全局只有一个配置管理器实例
- 避免重复加载和配置不一致问题

### 自动路径解析
- 自动识别项目根目录
- 支持从不同位置调用配置管理器

### 配置验证
- 自动验证必需的配置项
- 根据模型类型验证对应的API密钥

### 强制重新加载
- 支持 `override=True` 参数强制重新加载环境变量
- 确保配置修改后能立即生效

## 迁移说明

### 已删除的文件
- `mode/config_loader.py` (已备份到 `backup/old_config_loaders/`)
- `nlg/config_loader.py` (已备份到 `backup/old_config_loaders/nlg_config_loader.py`)
- `dataset_config.json` (已移动到 `backup/`)

### 修改的文件
- `app.py`: 使用统一配置管理器
- `mode/main.py`: 移除重复的配置加载
- `nlg/service.py`: 移除重复的配置加载
- `entity/config.py`: 添加配置重置方法

## 最佳实践

1. **配置只在应用启动时加载一次**
2. **所有模块通过 `Config` 类访问配置**
3. **配置修改后重启应用使其生效**
4. **使用 `get_config_info()` 获取配置状态**
5. **在开发环境中可以使用 `reload_config()` 重新加载配置**

## 故障排除

### 配置加载失败
1. 检查 `.env` 文件是否存在于项目根目录
2. 检查 `.env` 文件格式是否正确
3. 检查必需的配置项是否设置

### 配置不生效
1. 确认已重启应用
2. 检查是否有多个配置加载点
3. 使用 `get_config_info()` 查看当前配置状态
