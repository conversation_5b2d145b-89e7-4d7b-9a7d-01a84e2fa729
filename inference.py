#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能家居控制助手 - 改进版推理脚本

这个版本修复了JSON提取和解析的问题。

作者: AI Assistant
创建时间: 2025-07-25
"""

# 修复transformers库兼容性问题
import transformers_fix

import os
import json
import time
import logging
import warnings
import re
from typing import Dict, List, Optional, Tuple, Any

import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    GenerationConfig
)
from peft import PeftModel

# 忽略一些不重要的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('smart_home_inference.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SmartHomeAssistantV2:
    """
    智能家居控制助手类 - 改进版
    
    修复了JSON提取和解析的问题
    """
    
    def __init__(
        self, 
        base_model_path: str = "/opt/LLM_MODEL/Qwen2-1.5B-Instruct/qwen/Qwen2-1.5B-Instruct/",
        finetuned_model_path: str = "./output/Qwen1.5/checkpoint-100/",
        device: Optional[str] = None
    ):
        """初始化智能家居助手"""
        self.base_model_path = base_model_path
        self.finetuned_model_path = finetuned_model_path
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        
        # 模型和tokenizer
        self.tokenizer = None
        self.model = None
        
        # 系统提示词
        self.system_prompt = """你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

1. 输出必须是标准JSON对象，包含"function_calls"字段，值为JSON数组
2. 设备控制强制规则：
   - room参数必须精确匹配用户明确提到的位置词
   - 未提及位置时返回"默认"
   - 禁止位置推断（如"床头灯"不能推断为"卧室"）
   - 仅当用户明确说"全屋"、"所有设备"时才返回"all"
3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）
4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型
5. 输出纯JSON格式，不带解释或注释"""
        
        logger.info(f"初始化智能家居助手 - 设备: {self.device}")
        
    def load_model(self) -> bool:
        """加载模型和tokenizer"""
        try:
            logger.info("开始加载模型...")
            
            # 检查路径是否存在
            if not os.path.exists(self.base_model_path):
                raise FileNotFoundError(f"基础模型路径不存在: {self.base_model_path}")
            if not os.path.exists(self.finetuned_model_path):
                raise FileNotFoundError(f"微调模型路径不存在: {self.finetuned_model_path}")
            
            # 加载tokenizer
            logger.info("正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.base_model_path,
                use_fast=False,
                trust_remote_code=True,
                padding_side="left"
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # 加载基础模型
            logger.info("正在加载基础模型...")
            base_model = AutoModelForCausalLM.from_pretrained(
                self.base_model_path,
                torch_dtype=torch.bfloat16,
                trust_remote_code=True,
                device_map="auto",
                low_cpu_mem_usage=True
            )
            
            # 加载LoRA权重
            logger.info("正在加载LoRA微调权重...")
            self.model = PeftModel.from_pretrained(
                base_model,
                self.finetuned_model_path,
                torch_dtype=torch.bfloat16
            )
            
            # 设置为推理模式
            self.model.eval()
            
            logger.info("✅ 模型加载完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {e}")
            return False
    
    def extract_json_from_response(self, response: str) -> str:
        """
        从响应中提取JSON对象
        
        Args:
            response: 原始响应
            
        Returns:
            str: 提取的JSON字符串
        """
        # 首先尝试找到assistant标记后的内容
        if "assistant\n" in response:
            response = response.split("assistant\n")[-1]
        elif "<|im_start|>assistant\n" in response:
            response = response.split("<|im_start|>assistant\n")[-1]
        
        # 移除结束标记
        if "<|im_end|>" in response:
            response = response.split("<|im_end|>")[0]
        
        response = response.strip()
        
        # 使用正则表达式找到第一个完整的JSON对象
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, response)
        
        if matches:
            # 返回第一个匹配的JSON
            return matches[0]
        
        # 如果正则表达式失败，尝试手动解析
        if response.startswith("{"):
            brace_count = 0
            json_end = -1
            in_string = False
            escape_next = False
            
            for i, char in enumerate(response):
                if escape_next:
                    escape_next = False
                    continue
                    
                if char == "\\" and in_string:
                    escape_next = True
                    continue
                    
                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue
                    
                if not in_string:
                    if char == "{":
                        brace_count += 1
                    elif char == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break
            
            if json_end > 0:
                return response[:json_end]
        
        return response
    
    def predict(
        self, 
        user_input: str, 
        max_new_tokens: int = 128,
        temperature: float = 0.1,
        top_p: float = 0.9,
        do_sample: bool = True
    ) -> Tuple[str, float]:
        """进行推理预测"""
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("模型未加载，请先调用load_model()方法")
        
        try:
            start_time = time.time()
            
            # 格式化输入
            formatted_input = f"<|im_start|>system\n{self.system_prompt}<|im_end|>\n<|im_start|>user\n{user_input}<|im_end|>\n<|im_start|>assistant\n"
            
            # 编码输入
            inputs = self.tokenizer(
                formatted_input,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=384
            )
            
            # 移动到设备
            inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
            
            # 生成配置
            generation_config = GenerationConfig(
                max_new_tokens=max_new_tokens,
                temperature=temperature,
                top_p=top_p,
                do_sample=do_sample,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                repetition_penalty=1.1
            )
            
            # 生成响应
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs,
                    generation_config=generation_config
                )
            
            # 解码响应
            response = self.tokenizer.batch_decode(
                generated_ids, 
                skip_special_tokens=True
            )[0]
            
            # 提取JSON
            json_response = self.extract_json_from_response(response)
            
            inference_time = time.time() - start_time
            
            logger.info(f"推理完成，耗时: {inference_time:.3f}秒")
            return json_response.strip(), inference_time
            
        except Exception as e:
            logger.error(f"推理失败: {e}")
            return f"ERROR: {str(e)}", 0.0
    
    def validate_json_output(self, output: str) -> Tuple[bool, Optional[Dict]]:
        """验证输出是否为有效的JSON格式"""
        try:
            parsed_json = json.loads(output)

            # 检查标准格式：{"function_calls": [...]}
            if "function_calls" in parsed_json and isinstance(parsed_json["function_calls"], list):
                return True, parsed_json

            # 检查单个函数调用格式：{"name": "...", "arguments": {...}}
            elif "name" in parsed_json and "arguments" in parsed_json:
                # 转换为标准格式
                standard_format = {
                    "function_calls": [parsed_json]
                }
                return True, standard_format

            else:
                return False, None

        except json.JSONDecodeError:
            return False, None
    
    def chat(self, user_input: str, validate_json: bool = True) -> Dict[str, Any]:
        """与智能家居助手对话"""
        if not user_input.strip():
            return {
                "success": False,
                "error": "输入不能为空",
                "response": None,
                "inference_time": 0.0,
                "valid_json": False
            }
        
        # 进行推理
        response, inference_time = self.predict(user_input)
        
        # 验证JSON格式
        valid_json = False
        parsed_json = None
        if validate_json and not response.startswith("ERROR:"):
            valid_json, parsed_json = self.validate_json_output(response)
        
        return {
            "success": not response.startswith("ERROR:"),
            "response": response,
            "parsed_json": parsed_json,
            "inference_time": inference_time,
            "valid_json": valid_json,
            "user_input": user_input
        }


def main():
    """主函数 - 演示如何使用智能家居助手"""
    print("🏠 智能家居控制助手 V2 - 改进版推理演示")
    print("=" * 50)
    
    # 创建助手实例
    assistant = SmartHomeAssistantV2()
    
    # 加载模型
    if not assistant.load_model():
        print("❌ 模型加载失败，程序退出")
        return
    
    # 测试用例
    test_cases = [
        "打开客厅的灯",
        "把卧室空调温度调到26度",
        "启动睡眠模式",
        "你好"
    ]
    
    print("\n🧪 开始测试...")
    print("-" * 50)
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_input}")
        
        result = assistant.chat(test_input)
        
        if result["success"]:
            print(f"✅ 响应: {result['response']}")
            print(f"⏱️  推理时间: {result['inference_time']:.3f}秒")
            print(f"📋 JSON格式: {'有效' if result['valid_json'] else '无效'}")
            
            if result["parsed_json"]:
                print(f"🔧 解析结果: {json.dumps(result['parsed_json'], ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 错误: {result['response']}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")


if __name__ == "__main__":
    main()
