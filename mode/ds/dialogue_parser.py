# mode/ds/dialogue_parser.py
import re
from datetime import datetime

def parse_dialogue(text, default_role="主人"):
    text = text.strip()

    if re.search(r'.*?:', text):
        pattern = r'(.*?):(.*?)(?=\n\S+:|\Z)'
        matches = re.findall(pattern, text, re.DOTALL)

        result = []
        for speaker, content in matches:
            result.append({
                "role": speaker.strip(),
                "content": content.strip()
            })
        return result

    return [{
        "role": default_role,
        "content": text.strip()
    }]