# mode/ds/llm_processor.py
import time
import json
from qwen_agent.llm import get_chat_model
from flask import current_app
from mode.ds.function_extractor import extract_json_objects
from mode.ds.response_formatter import format_response
from entity.config import Config
from mode.ds import tools
from json_repair import repair_json


class LLMProcessor:
    def __init__(self):
        self.config = Config

    def process_llm_call(self,messages,traceId:str,query:str):
        llm = get_chat_model({
            "model": self.config.model,
            "model_server": self.config.model_server,
            "api_key": self.config.api_key,
        })
        
        functions = [tool["function"] for tool in tools.get_tools(self.config)]

        start_time = time.time()
        responses = []
        for chunk in llm.chat(
            messages=messages,
            functions=functions,
            extra_generate_cfg=dict(parallel_function_calls=True, response_format={"type": "json_object"},temperature=0.4,top_p=0.4),
        ):
            pass
        responses.extend(chunk)

        elapsed_time = time.time() - start_time
        current_app.logger.info(f"traceId: {traceId} ,LLM 推理完成，耗时: {elapsed_time:.2f} 秒")
        current_app.logger.info(f"traceId: {traceId} ,LLM 回复:"+json.dumps(responses, indent=2, ensure_ascii=False))
        results = []
        for message in responses:
            content = message.get("reasoning_content","")
            if not content:
                continue

            try:
                content = content.replace('}, "name":',  '}}, { "name":').replace('}, "arguments":',  '}}, { "arguments":').replace(',"}',  '}')
                content = repair_json(content)  # 自动修复
                data = json.loads(content)
                if isinstance(data, dict) and 'function_calls' in data:
                    results.extend([fc.get('arguments') for fc in data['function_calls']])
                elif isinstance(data, list):
                    results.extend([item.get('arguments') for item in data])
                else:
                    parsed_objs = extract_json_objects(content)
                    results.extend([obj.get('arguments', obj.get('response',obj)) for obj in parsed_objs])

            except json.JSONDecodeError:
                parsed_objs = extract_json_objects(content)
                results.extend([obj.get('arguments') for obj in parsed_objs if 'arguments' in obj])

        return format_response(results, messages[len(messages)-1].get("content"),self.config.DEVICE_LIST,self.config.device_names,self.config.devices)