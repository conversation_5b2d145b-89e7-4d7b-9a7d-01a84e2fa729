
def process_field(k, v):
    """
    通用字段处理器，用于根据字段名和值进行统一转换
    """
    if k == "room" and v == "默认":
        return None
    if k == "pos" and v in ("伦敦", "默认"):
        return None
    if k == "device" and v == "":
        return None
    if k == "value" and v in ("", None, "0"):
        return None

    # 处理带"k"单位的数值，如"2k" → 2000
    if k == "value" and isinstance(v, str) and v.lower().endswith("k"):
        return v[:-1]  # 去掉最后的 'k'
        
    # 其他自定义规则（如"度"等）可继续添加
    if k == "value" and v.endswith("度"):
        return v.replace("度", "")
    
    return v


def parse_and_reformat(item_str):
    """
    根据内容判断是否为 getWeather 或 getLiving 的响应，并按需格式化
    """

    # 判断是否是 getWeather 或 getLiving 的意图
    if "domain：天气" in item_str:
        return _reformat_weather_or_living(item_str)
    elif "domain：生活指数" in item_str:
        return _reformat_weather_or_living(item_str)
    elif "intent：other" in item_str:
        return _reformat_other()
    else:
        # 其他类型不做处理，直接返回原字符串
        return item_str

def _reformat_other():
      return "NOT_SUPPORTED"

def device_not_found(deviceName:str):
    return "DEVICE_NOT_FOUND"+"&"+deviceName

def _reformat_weather_or_living(item_str):
    """
    特殊处理 getWeather 和 getLiving 的返回格式
    示例：
      input: intent：查询天气状况；domain：天气；pos：北京；offset：（day：+0）～（day：+1）；unit：timeRange
      output: intent：查询天气状况；timeRange：【（day：+0）～（day：+1）】；pos：北京

      input: intent：查询紫外线指数；domain：生活指数；pos：上海；offset：0；unit：day
      output: intent：查询紫外线指数；day：+0；pos：上海
    """
    parts = [p.strip() for p in item_str.split("；")]
    data = {}

    for part in parts:
        if "：" not in part:
            continue
        key, value = part.split("：", 1)
        data[key] = value

    new_parts = []

    for key, value in data.items():
        if key == "intent" or key == "pos":
            new_parts.append(f"{key}：{value}")
        elif key == "unit":
            if value == "timeRange":
                time_range = data.get("offset", "")
                new_parts.append(f"timeRange：【{time_range}】")
            elif value in ["year", "month", "day", "week", "hour", "minute", "second"]:
                offset = data.get("offset", "0")
                # 如果 offset 是纯数字或带符号的数字，则加 '+'
                if offset.lstrip('-').isdigit():
                    offset = f"+{offset}" if not offset.startswith('-') else offset
                if offset.startswith("(") and offset.endswith(")"):
                    offset = offset.replace("(", "").replace(")", "").replace(value+":", "")
                new_parts.append(f"{value}：{offset}")

        # 其他字段保留
        elif key not in ["unit", "offset"]:
            new_parts.append(f"{key}：{value}")

    return "；".join(new_parts)

def format_response(parsed_objs,query:str, domain_list: list = None, device_names: list = None,devices: list = None):
    """
    格式化响应输出，支持 dict/list 类型输入
    :param parsed_objs: 解析后的对象（dict 或 list）
    :param domain_list: 支持的领域列表
    :param device_names: 支持的设备名列表
    :return: 格式化后的响应
    """
    if domain_list is None:
        domain_list = []
    if device_names is None:
        device_names = []

    # 默认支持的系统领域
    system_domains = {"场景", "天气", "生活指数", "闲聊", "对话","all"}
    domain_set = set(domain_list) | system_domains

    if not parsed_objs:
        return {
            "code": "SUCCESS",
            "msg": "成功",
            "data": {
                "response": []
            }
        }

    try:
        response_list = []

        # 统一转换为 list 处理
        items = parsed_objs if isinstance(parsed_objs, list) else [parsed_objs]

        for obj in items:
            if not isinstance(obj, dict):
                response_list.append(str(obj))
                continue

            # 过滤无效字段
            filtered_obj = {
                k: process_field(k, v)
                for k, v in obj.items()
                if process_field(k, v) is not None
            }

            domain = filtered_obj.get("domain")
            device_name = filtered_obj.get("device")

            # 域名检查
            if domain and domain not in domain_set:
                response_list.append(_reformat_other())
                continue
            old_list = ['昨天', '前天', '过去']

            if domain in ["天气", "生活指数"] and  any(old_day in query for old_day in old_list):
                 filtered_obj["offset"] = "-1"
                 filtered_obj["unit"] = "day"

            # 设备名检查
            if device_name and device_name == domain:
                filtered_obj["device"] = ""
                device_name = None

            # if device_name and device_name not in device_names:
            #     response_list.append(device_not_found(device_name))
            #     continue
            for  device in devices:
                if device["name"] == device_name:
                    old_intent = filtered_obj["intent"].replace(filtered_obj["domain"], "")
                    if filtered_obj["intent"] in["打开插座", "关闭插座","打开开关","关闭开关","打开灯","关闭灯","打开窗帘","关闭窗帘","暂停窗帘","打开通断器","关闭通断器","打开安防","关闭安防","打开空调","关闭空调","打开新风","关闭新风","打开杀菌","关闭杀菌","打开地暖","关闭地暖"]:
                        filtered_obj["domain"] =  device["intentDomainName"]
                        filtered_obj["intent"] = old_intent+ device["intentDomainName"]

            # 转换为字符串并格式化
            item_str = "；".join(f"{k}：{v}" for k, v in filtered_obj.items())
            reformatted = parse_and_reformat(item_str)
            response_list.append(reformatted)

        return {
            "code": "SUCCESS",
            "msg": "成功",
            "data": {
                "response": list(set(response_list))
            }
        }

    except Exception as e:
        return {
            "code": "ERROR",
            "msg": f"识别失败: {str(e)}",
            "data": {
                "response": []
            }
        }