# mode/hw/response_formatter.py

def process_field(k, v):
    """
    通用字段处理器，用于根据字段名和值进行统一转换
    """
    if k == "room" and v == "默认":
        return None
    if k == "pos" and v == "伦敦":
        return None
    return v


def parse_and_reformat(item_str):
    """
    根据内容判断是否为 getWeather 或 getLiving 的响应，并按需格式化
    """

    # 判断是否是 getWeather 或 getLiving 的意图
    if "domain：天气" in item_str:
        return _reformat_weather_or_living(item_str)
    elif "domain：生活指数" in item_str:
        return _reformat_weather_or_living(item_str)
    else:
        # 其他类型不做处理，直接返回原字符串
        return item_str

def _reformat_weather_or_living(item_str):
    """
    特殊处理 getWeather 和 getLiving 的返回格式
    示例：
      input: intent：查询天气状况；domain：天气；pos：北京；offset：（day：+0）～（day：+1）；unit：timeRange
      output: intent：查询天气状况；timeRange：【（day：+0）～（day：+1）】；pos：北京

      input: intent：查询紫外线指数；domain：生活指数；pos：上海；offset：0；unit：day
      output: intent：查询紫外线指数；day：+0；pos：上海
    """
    parts = [p.strip() for p in item_str.split("；")]
    data = {}

    for part in parts:
        if "：" not in part:
            continue
        key, value = part.split("：", 1)
        data[key] = value

    new_parts = []

    for key, value in data.items():
        if key == "intent" or key == "pos":
            new_parts.append(f"{key}：{value}")
        elif key == "unit":
            if value == "timeRange":
                time_range = data.get("offset", "")
                # 新增逻辑：去除 offset 中已有的 【】 再包裹一次
                time_range = time_range.strip("【】")  # 去除两边的 【】
                new_parts.append(f"timeRange：【{time_range}】")
            elif value in ["year", "month", "day", "week", "hour", "minute", "second"]:
                offset = data.get("offset", "0").strip("+")
                new_parts.append(f"{value}：+{offset}")
        elif key not in ["unit", "offset"]:
            new_parts.append(f"{key}：{value}")

    return "；".join(new_parts)
def format_response(parsed_objs, input_text=""):
    if not parsed_objs:
        return {
            "code": "SUCCESS",
            "msg": "成功",
            "data": {}
        }

    try:
        response_list = []

        for obj in parsed_objs:
            if isinstance(obj, dict) and 'response' in obj:
                items = obj['response']
                if isinstance(items, list):
                    for item in items:
                        if isinstance(item, dict):
                            filtered_obj = {
                                k: process_field(k, v)
                                for k, v in item.items()
                                if process_field(k, v) is not None
                            }

                            # 转换为字符串格式
                            item_str = "；".join(f"{k}：{v}" for k, v in filtered_obj.items())

                            # 对特定类型做特殊格式化
                            reformatted = parse_and_reformat(item_str)
                            response_list.append(reformatted)

        return {
            "code": "SUCCESS",
            "msg": "成功",
            "data": {
                "response": response_list
            }
        }
    except Exception as e:
        return {
            "code": "ERROR",
            "msg": f"识别失败: {str(e)}",
            "data": {}
        }