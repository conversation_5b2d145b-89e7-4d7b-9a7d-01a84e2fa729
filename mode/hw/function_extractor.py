# mode/ds/function_extractor.py
import json
import regex as re
from mistletoe import Document
from flask import current_app
import demjson3 as demjson
def extract_json_objects(text: str):
    """
    提取文本中的多个 JSON 函数调用对象。
    支持：
      - Markdown 代码块（```json ... ```）
      - <response> 标签包裹的内容
      - 纯文本中包含的多行 JSON
    返回值为 list of dict
    """

    # 尝试提取 <response> 中的内容
    response_match = re.search(r"<response>\s*(.*?)\s*</response>", text, re.DOTALL)
    if response_match:
        content_block = response_match.group(1).strip()
        lines = [line.strip() for line in content_block.split('\n')]
        parsed_list = []
        for line in lines:
            if not line:
                continue
            try:
                obj = json.loads(line)
                parsed_list.append(obj)
            except json.JSONDecodeError:
                pass
        if parsed_list:
            return parsed_list

    # 尝试提取 Markdown 代码块中的 JSON
    markdown_match = re.search(r"```json\s*(.*?)\s*```", text, re.DOTALL)
    if markdown_match:
        content_block = markdown_match.group(1).strip()
        try:
            obj = json.loads(content_block)
            if isinstance(obj, list):
                return obj
            elif isinstance(obj, dict):
                return [obj]
        except json.JSONDecodeError:
            pass

    # 如果都没有，则尝试从纯文本中提取所有 { ... } 结构（使用 regex 支持递归）
    json_blocks = re.findall(r'\{(?:[^{}]|(?R))*\}', text, re.DOTALL)
    parsed_list = []

    for block in json_blocks:
        cleaned_block = block.replace('\n', '').replace(' ', '')
        try:
            obj = demjson.decode(cleaned_block)
            parsed_list.append(obj)
        except Exception as e:
            current_app.logger.error(f"JSON 解析失败（raw block）: {e}")

    return parsed_list