# mode/hw/dialogue_parser.py
import re
import json

def parse_dialogue(text, default_role="主人"):
    """
    解析多轮对话文本，支持以下格式：
    - 普通对话："主人：你好\n助手：您好"
    - 卡片消息："主人：{\"payload\":{\"cardText\":\"打开就餐场景\"},\"type\":4,..."
    """

    # 清理原始文本
    text = text.strip()

    # 新增：处理卡片消息（冒号后紧跟JSON）
    card_match = re.match(r'^(.+?)[：:]\s*({[\s\S]*})$', text)  # ←← 这里修改为 [：:] 支持中英文冒号
    if card_match:
        speaker = card_match.group(1).strip()
        raw_json = card_match.group(2)

        try:
            payload = json.loads(raw_json)
            card_text = payload.get("payload", {}).get("cardText")
            if card_text:
                return [{
                    "role":  speaker.strip(),
                    "content": card_text.strip()
                }]
        except json.JSONDecodeError:
            pass  # JSON 解析失败时继续走普通解析流程

    if re.search(r'.*[：:]', text):  # ←← 支持中英文冒号
            pattern = r'(.*?)[：:](.*?)(?=\n[^：:]+[：:]|\Z)'  # ←← 支持中英文冒号
            matches = re.findall(pattern, text, flags=re.DOTALL)

            result = []
            for speaker, content in matches:
                result.append({
                    "role": speaker.strip(),
                    "content": content.strip()
                })
            return result

    return [{
        "role": default_role,
        "content": text.strip()
    }]