# hw_llm_processor.py

import time
import re
import json
from openai import OpenAI
from flask import current_app
from mode.hw.function_extractor import extract_json_objects
from mode.hw.response_formatter import format_response
from entity.config import Config

class HwLLMProcessor:
    def __init__(self):
        self.config = Config

    def process_llm_call(self, messages,traceId:str):
        #使用OpenAI SDK调用华为盘古模型
        client = OpenAI(
            api_key=self.config.api_key,
            base_url=self.config.model_server
        )

        start_time = time.time()
        
        # 发起非流式请求
        response = client.chat.completions.create(
            model=self.config.model,
            messages=messages,
            stream=False
        )

        #响应解析和预处理
        responses = []
        if response.choices:
            for choice in response.choices:
                message = choice.message
                if message:
                    responses.append(message.model_dump())

        elapsed_time = time.time() - start_time
        current_app.logger.info(f"HW LLM 推理完成，traceId:{traceId},耗时: {elapsed_time:.2f} 秒")
        current_app.logger.info("traceId:{traceId},HW LLM 回复:" + json.dumps(responses, indent=2, ensure_ascii=False))

        results = []
        for message in responses:
            content = message.get("content", "")
            if not content:
                continue

            try:
                # 清除 Markdown 包裹
                content = re.sub(r'^\s*```json\s*', '', content).strip()
                content = re.sub(r'\s*```\s*$', '', content).strip()

                data = json.loads(content)

                if isinstance(data, dict) and 'function_calls' in data:
                    formatted_response = []

                    for fc in data['function_calls']:
                        args = fc.get('arguments', {})

                        # 构造字段字典
                        obj = {
                            "intent": args.get("intent"),
                            "content": args.get("content"),
                            "domain": args.get("domain"),
                            "pos": args.get("pos"),
                            "room": args.get("room"),
                            "device": args.get("device"),
                            "offset": args.get("offset"),
                            "unit": args.get("unit"),
                            "value":  args.get("value"),
                            "scene":  args.get("scene"),
                        }

                        # 过滤掉空值字段
                        filtered_obj = {k: v for k, v in obj.items() if v not in (None, "", b'', False, True)}
                        formatted_response.append(filtered_obj)

                    results.append({
                        "response": formatted_response
                    })

                elif isinstance(data, list):
                    results.extend([item.get('arguments') for item in data])
                else:
                    parsed_objs = extract_json_objects(content)
                    results.extend([obj.get('arguments', obj.get('response', obj)) for obj in parsed_objs])

            except json.JSONDecodeError:
                parsed_objs = extract_json_objects(content)
                results.extend([obj.get('arguments') for obj in parsed_objs if 'arguments' in obj])

        return format_response(results)