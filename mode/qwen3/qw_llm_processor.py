# mode/ds/llm_processor.py
import time
import json
from http import HTTPStatus
import dashscope
from flask import current_app
from mode.qwen3.response_formatter import format_response
from entity.config import Config
from mode.qwen3 import tools

class QWLLMProcessor:
    def __init__(self):
        self.config = Config

    def process_llm_call(self,messages,traceId:str,query:str):

        start_time = time.time()
        functions =  tools.get_tools(self.config)
        response = dashscope.Generation.call(
            model=self.config.model,
            api_key=self.config.api_key,
            messages=messages,
            tools = functions,
            stream=False,
            result_format='message',  # 将返回结果格式设置为 message
            top_p=0.8,
            temperature=0.4,
            enable_search=False,
            enable_thinking=False,
            parallel_tool_calls=True
        )
        choices =[]
        if response.status_code == HTTPStatus.OK:
             choices = response.output.choices
        else:
            current_app.logger.error('Request id: %s, Status code: %s, error code: %s, error message: %s' % (
                response.request_id, response.status_code,
                response.code, response.message
            ))
        results = []

        elapsed_time = time.time() - start_time
        current_app.logger.info(f"QW LLM 推理完成，traceId:{traceId},耗时: {elapsed_time:.2f} 秒")
        current_app.logger.info(f"traceId:{traceId},QW LLM 回复:" + json.dumps(response, indent=2, ensure_ascii=False))
        for choice in choices:
            message = choice["message"]

            # 优先解析 tool_calls
            tool_calls = message.get("tool_calls")
            if tool_calls:
                for call in tool_calls:
                    try:
                        args = json.loads(call["function"]["arguments"])
                        results.append(args)
                    except json.JSONDecodeError as e:
                        current_app.logger.error(f"JSON 解码失败：{e}")
                        continue
                continue

            # 如果没有 tool_calls，回退到解析 content
            content = message.get("content", "")
            if content and content.strip():  # 确保content不为空且不只是空白字符
                try:
                    content_json = json.loads(content)
                    function_calls = content_json.get("function_calls", None)
                    if function_calls is None:
                        results.append(content_json.get("arguments"))
                        continue
                    for call in function_calls:
                        results.append(call.get("arguments"))
                except json.JSONDecodeError as e:
                    current_app.logger.error(f"content JSON 解码失败：{e}, content: '{content}'")
                    continue


        return format_response(results,messages[len(messages)-1].get("content"), self.config)