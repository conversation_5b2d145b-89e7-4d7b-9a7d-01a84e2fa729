# mode/tools.py
from entity.config import Config

def get_tools(config: Config):

    return [
                {
                "type": "function",
                "function": {
                    "name": "openOrClose",
                    "description": "打开或者关闭设备。设备识别规则："
                        "1. 用户输入格式通常为：<空间位置><设备类型><编号>（如'走廊地暖1'）"
                        "2. 提取规则："
                        "   - 空间位置（room）：识别位置关键词（客厅、卧室、走廊等）"
                        "   - 设备类型（domain）：识别设备类型（灯、地暖等）"
                        "   - 设备昵称（device）：提取设备编号或自定义名称"
                        "3. 特殊规则："
                        "   - 当无法匹配任何设备类型时（如'打开厕所'），domain 返回空字符串，并将宾语（'厕所'）作为 device 返回"
                        "   - 当设备类型和昵称相同时（如'打开卧室灯'），device 返回空"
                        "   - 当提到'全屋、全家'时，room 返回 'all'"
                        " - 当输入暂停窗帘时，intent需识别成暂停窗帘，不能识别成关闭窗帘"
                        "- 当输入信息里有筒灯、射灯、灯带、线条灯、折叠格栅灯、格栅灯，将此信息作为设备昵称device输出"
                        "示例："
                        "   - '打开走廊地暖1' → domain: '地暖', room: '走廊', device: '地暖1'"
                        "   - '开客厅灯1' → domain: '灯', room: '客厅', device: '灯1'"
                        "   - '打开卧室灯' → domain: '灯', room: '卧室', device: ''"
                         "  - '打开床头灯' → domain: '灯', device: '床头灯'"
                         "  - '打开所有灯' → intent：打开灯；domain：灯"
                        "  - '打开全部灯' → intent：打开灯；domain：灯"
                        "   - '打开全家的灯' →intent：打开灯, domain: '灯', room: 'all'"
                         "  - '打开全屋的灯2' →intent：打开灯, domain: '灯', room: 'all',device: '灯2'"
                        '   - "打开全家筒灯与射灯" → "intent：打开灯；domain：灯；room：all；device：筒灯", "intent：打开灯；domain：灯；room：all；device：射灯"'
                        "   - '打开厕所' → domain: '', device: '厕所' ",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "intent": {
                                "type": "string",
                                "enum": ["打开插座", "关闭插座","打开开关","关闭开关","打开灯","关闭灯","打开窗帘","关闭窗帘","暂停窗帘","打开通断器","关闭通断器","打开安防","关闭安防","打开空调","关闭空调","打开新风","关闭新风","打开杀菌","关闭杀菌","打开地暖","关闭地暖"],
                                "description": "执行意图:打开插座,关闭插座,打开开关,关闭开关,打开灯,关闭灯,打开窗帘,关闭窗帘,暂停窗帘,打开通断器,关闭通断器,打开安防,关闭安防,打开空调,关闭空调,打开新风,关闭新风,打开杀菌,关闭杀菌,打开地暖,关闭地暖"
                            },
                            "domain": {
                                "type": "string",
                                "enum": ["插座", "开关","通断器","安防","灯","窗帘","空调","新风","地暖"],
                                 "description": "来自预定义枚举列表里的枚举值，不要额外扩展，否则返回空字符串",
                            },
                            "room": {
                                "type": "string",
                                "enum": config.space,
                                "description": "空间位置识别规则：来自预定义枚举列表里的枚举值 "
                                    "1. 提取位置关键词（客厅、卧室、走廊、主卧、阳台等）"
                                    "2. 当用户说'全屋'、'全家'时返回'all'"
                                    "3. 当说'所有、全部'时或者未明确位置时返回'默认'",
                        
                            },
                            "device":{
                                "type": "string",
                                "enum": config.device_names,
                                "description": "设备昵称提取规则：\n"
                                                "设备昵称优先通过枚举值匹配，如未匹配到则通过一下规则进行提取："
                                                "1.当 domain 为空时，直接返回宾语（如'厕所'）"
                                                "2. **单设备**：\n"
                                                "   - 有编号 → 返回`<设备类型><编号>`（如'灯1'）\n"
                                                "   - 无编号且名称=domain → 返回空字符串（如'卧室灯'）\n"
                                                "   - 无编号且名称≠domain → 返回设备昵称（如'床头灯'）\n"
                                                "   - 当输入信息带'的'且名称=domain，如'主卧的灯'→ 返回空字符串,如'主卧的壁灯'→ 返回壁灯\n"
                                                "3. **多设备**：\n"
                                                "   - 需拆分连接词（'和'、'与'、'以及'）后的每个设备"
                                                "   - 优先返回首个名称≠domain的设备昵称（如'氛围灯'）\n"
                                                "4. **自定义名称**：直接返回自定义名（如'主灯'）",
                            }
                        },
                        "required": ["intent"]
                    }
                }
            },

            {
                "type": "function",
                "function": {
                    "name": "setHighOrLow",
                    "description": "设置或者调整灯亮度,灯色温,空调风速，新风风速,地暖温度,空调温度，窗帘开合度，调大点儿、调小点儿，高点儿，低点儿，制热/制冷/送风/除湿/自动",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "intent": {
                                "type": "string",
                                "enum": ["设置亮度","调高亮度","调低亮度","设置色温","调高色温","调低色温","设置开合度","调大开合度","调小开合度","设置温度","调高温度","调低温度","设置风速","调高风速","调低风速","调高地暖","调低地暖","设置地暖温度","设置模式"],
                                "description": "执行意图选择规则："
                                            "- 用户说'调高/调低'且不含'到/自动/强速/高速/中速/低速'字 ➜ 使用【调高亮度/调低风速】等"
                                            "- 用户说'调到/设置为/设到' ➜ 使用【设置亮度/设置风速】等"
                                            "- 用户说'制热/制冷/送风/除湿/自动等' ➜ 使用【设置模式】，value返回（制热/制冷/送风/除湿/自动等）"
                                            "- 用户说风速 设置为/调'自动/强/高/中/低等' ➜ 使用【设置模式】，value返回（自动/强/高/中/低等）",
                            },
                            "domain": {
                                "enum": ["插座", "开关","通断器","安防","灯","窗帘","空调","新风","地暖"],
                                "description": "来自预定义枚举列表里的枚举值，不要额外扩展，否则返回空字符串",
                            },
                            "value": {
                                "type": "string",
                                "description": '调节值提取规则：'
                                               '1. **必须提取所有数值**：当输入包含数值（如"调高1000"、"增加500"）时，必须返回该数值'
                                               '2. **中文数字转换**：将中文数字转换为阿拉伯数字（如"一千"→"1000"，"两百"→"200")'
                                               '3. **带单位处理**：'
                                               '   - 当含"度"（色温）、"摄氏度"（温度）等单位时，保留数值部分'
                                               '   - 示例："调高一千度" → "1000"'
                                               ' 其他情况（如具体数值"50%"），返回数字或数字+百分号'
                                               '4. **调高/调低+数值规则**：'
                                               '   - 用户说"调高一千" → 返回"1000"'
                                               '   - 用户说"降低五百" → 返回"500"'
                                               '5. 其他规则：'
                                               '   - 当指定"最高/最大/最亮"时 → 返回"max"'
                                               '   - 当指定"最低/最小/最暗"时 → 返回"min"'
                                               '   - 当为百分比（如"50%"）时 → 返回"50%"'
                                               '   - 当为模式（如"制热"）时 → 返回模式名称',
                            },
                            "room": {
                                "type": "string",
                                "enum": config.space,
                                "description": "空间位置识别规则：来自预定义枚举列表里的枚举值"
                                    "1. 提取位置关键词（客厅、卧室、走廊、主卧、阳台等）"
                                    "2. 当用户说'全屋'、'全家'时返回'all'"
                                    "3. 当说'所有、全部'时或者未明确位置时返回'默认'",
                            },
                          "device":{
                                "type": "string",
                                "enum": config.device_names,
                                "description": "设备昵称提取规则：\n"
                                                "优先通过枚举值匹配，如未匹配到则通过一下规则进行提取："
                                                "1.当 domain 为空时，直接返回宾语（如'厕所'）"
                                                "2. **单设备**：\n"
                                                "   - 有编号 → 返回`<设备类型><编号>`（如'灯1'）\n"
                                                "   - 无编号且名称=domain → 返回空字符串（如'卧室灯'）\n"
                                                "   - 无编号且名称≠domain → 返回设备昵称（如'床头灯'）\n"
                                                "   - 当输入信息带'的'且名称=domain，如'主卧的灯'→ 返回空字符串,如'主卧的壁灯'→ 返回壁灯\n"
                                                "3. **多设备**：\n"
                                                "   - 拆分连接词（'和'、'与'、'以及'）后的每个设备\n"
                                                "   - 优先返回首个名称≠domain的设备昵称（如'氛围灯'）\n"
                                                "4. **自定义名称**：直接返回自定义名（如'主灯'）",
                          }
                        },
                        "required": ["intent", "domain"]
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "scene",
                    "description": "场景，执行设备场景",
                    "parameters": {
                        "type": "object",
                        "properties": {
                        "scene": {
                                "type": "string",
                                "enum": config.SCENE_LIST,
                                "description": f'场景名称，在{config.SCENE_LIST}列表中精确查找匹配项，若没有匹配到直接使用用户输入的原始名称（打开或者执行等动词后的名称，例如输入"执行测试专用开灯场景",则返回"测试专用开灯场景"）'
                                             '当匹配到通用场景规则：'
                                            ' - 离家模式 → "我走了", "我出门了", "上班去了", "外出", "我离家了"'
                                            ' - 回家模式 → "到家了", "我回家了", "我到家了"'
                                            ' - 用餐模式 → "吃饭", "我吃饭了", "我要吃饭了"'
                                            '- 会客模式 → "有客人来了", "有客人了", "来客人了"'
                                            '- 晨起模式 → "我要起床了", "我起床了", "我醒了"'
                                            ' - 睡眠模式 → "我要睡了", "我要睡觉了"',
                            },
                            "domain":  {
                                "type": "string",
                                "default": "场景",
                                "description": "属性：场景，默认为：场景，必传",
                            },
                            "intent": {
                                "type": "string",
                                "enum": ["打开场景"],
                                "description": '执行意图：打开场景',
                            },
                        },
                            "required": ["scene","domain","intent"],
                    },
                },
            }, 
            {
                "type": "function",
                "function": {
                    "name": "getWeather",
                     "description": "仅查询天气情况查，询预定义的天气指数类型，,注意：不处理现在几点，当前时间荐等开放性问题，规则如下："
                  "1. **必须精确匹配枚举值**：intent 参数必须严格使用预定义值，如'查询天气状况'，禁止修改为'查询天气情况'等近似表达"
                  "2. **不允许自定义意图**：即使语义相似，也必须使用预定义枚举",
                "parameters": {
                    "properties": {
                        "intent": {
                            "type": "string",
                            "enum": config.WEATHER_INDEX,
                            "description": "必须从以下预定义值中精确选择："
                                        f"{', '.join(config.WEATHER_INDEX)}"
                                        "\n规则："
                                        "1. 即使输入有细微差异（如'天气情况'），也必须返回最接近的预定义值"
                                        "2. 禁止返回枚举之外的任何值"
                        },
                            "domain":  {
                                "type": "string",
                                "default": "天气",
                                "description": "必须返回'天气'",
                            },
                            "pos": {
                                "type": "string",
                                "description": '查询天气的地点，默认为伦敦',
                            },
                            "offset":{
                                "type": "string",
                                "description": "查询时间偏移量规则,若为具体某一天，**必须带符号**：今天值为'+0', 明天值为 '+1', 后天值为'+2', 昨天值为'-1'，比如后天天气怎么样必须为 +2，若为时间范围,比如未来两天或者近两天值格式必须为【（day：+1）～（day：+2）】，不能返回【+1～+2】， 比如未来三天或者近三天值格式必须为【（day：+1）～（day：+3）】，不能返回【+1～+3】，比如5月20到5月23的天气，值格式为【（month：5；day：20）～（month：5；day：23）】，"
                                     "本周值为+0，比如未来一周或者近一周值格式必须为 +1 ， 比如未来两周或者近两周值格式必须为 +2",
                            
                            },
                            "unit": {
                                "type": "string",
                                "enum": ["pos", "year", "month", "day", "week", "hour", "minute", "second","timeRange"],
                                "description": "查询天气指数的时间单位，查询时间范围则返回timeRange",
                            },
                        },
                    "required": ["intent","domain","pos","offset","unit"],
                    },
                },
            },

            {
                "type": "function",
                "function": {
                    "name": "getLiving",
                    "description":  "仅查询预定义的生活指数类型：查询限行，查询化妆指数，查询紫外线指数，查询感冒指数，查询洗车指数，查询穿衣指数, 查询运动指数,查询钓鱼指数,注意：不处理景点推荐、美食推荐等开放性问题",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "intent": {
                                "type": "string",
                                "enum": config.LIVING,
                                "description": "必须从以下预定义值中精确选择："
                                        f"{', '.join(config.LIVING)}"
                                        "\n规则："
                                        "1. 即使输入有细微差异（如'穿衣指南'），也必须返回最接近的预定义值"
                                        "2. 禁止返回枚举之外的任何值"
                            },
                            "domain":  {
                                "type": "string",
                                "default": "必须生活指数",
                                "description": "返回'生活指数'",
                            },
                            "pos": {
                                "type": "string",
                                "description": '查询生活指数的地点，默认为伦敦',
                            },
                        
                            "offset":{
                                "type": "string",
                                "description": "查询时间偏移量规则,若为具体某一天，**必须带符号**：今天值为'+0', 明天值为 '+1', 后天值为'+2', 昨天值为'-1'，比如后天天气怎么样必须为 +2，若为时间范围,比如未来两天或者近两天值格式必须为【（day：+1）～（day：+2）】，不能返回【+1～+2】， 比如未来三天或者近三天值格式必须为【（day：+1）～（day：+3）】，不能返回【+1～+3】，比如5月20到5月23的天气，值格式为【（month：5；day：20）～（month：5；day：23）】，"
                                    "本周值为+0，比如未来一周或者近一周值格式必须为 +1 ， 比如未来两周或者近两周值格式必须为 +2",
                            
                            },
                            "unit": {
                                "type": "string",
                                "enum": ["pos", "year", "month", "day", "week", "hour", "minute", "second","timeRange"],
                                "description": "查询生活指数的时间单位，查询时间范围则返回timeRange",
                            },
                        },
                            "required": ["intent","domain","pos","offset","unit"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "chat",
                     "description": f"""处理开放性问题时必须直接提供完整答案，规则如下：
                                1. **绝对禁止反问**：不允许出现'呢？''吗？''您觉得'等句式
                                2. **必须包含具体内容**：
                                - 推荐类问题：至少3个具体推荐项（名称+简要描述）
                                - 故事类问题：输出完整故事（不少于50字）
                                - 知识类问题：提供准确信息（如历史事实）
                                3. **位置处理**：使用{config.home_address}回答"附近"类问题
                                4. **回答质量要求**：
                                - 推荐类：包含名称、类型、特色
                                - 故事类：有明确开头-发展-结尾
                                - 知识类：引用可靠来源
                                 5. **时间查询规则**：
                                    - 当用户询问当前时间、日期、星期几时（如'现在几点''今天几号''今天星期几'），必须使用此函数
                                 6. **禁止转交**：不得将时间查询转交给其他函数（如getWeather）处理
                                """,
                    "parameters": {
                        "properties": {
                            "intent": {
                                "description": "固定为'闲聊'，表示开放式对话"
                            },
                            "content": {
                                "description":  "回答规则："
                                "1. 直接提供完整答案（如餐厅列表、故事全文）"
                                "2. 包含必要信息：位置、推荐理由等"
                                "3. 格式：文本段落（非列表）"
                                "4. 禁止出现：'呢？' '吗？' '您觉得'等反问句式"
                            },
                            "domain": {
                                "description": "固定为'闲聊'"
                            }
                        },
                        "required": ["intent","content","domain"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "queryDevice",
                    "description": "设备查询",
                    "parameters": {
                        "type": "object",
                        "properties": {
                        "intent": {
                                "type": "string",
                                "enum": ["设备数量查询"],
                                "description": '设备查询意图，比如查询有多少个设备就是设备数量查询，查询设备在线状态，亮度值等属性时就是状态查询',
                            },
                        "domain": {
                                "type": "string",
                                "enum": config.DEVICE_LIST,
                                "description": "设备类型字段，用于指定查询的目标设备。"
                                            "当用户明确提到具体设备时（如'灯'、'空调'），返回对应的设备名称；"
                                            "若查询未指明具体设备（例如'有多少设备'、'全屋的设备数量'），则统一返回 'all' 表示所有设备；"
                                            "若查询为'全屋有多少灯'，则返回'灯'。"
                            },
                            "room": {
                                "type": "string",
                                "enum": config.space,
                                "description": "设备所属空间，含有全屋，全家等全集词汇时，返回all，当为所有时或者未指定时默认为'默认'"
                            }
                        },
                            "required": ["intent","domain","room"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "dialog",
                    "description": "对话",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "intent": {
                                "type": "string",
                                "enum": ["终止对话","重新开始","敏感词"],
                                "description": "对话意图，比如用户说先不说了，退下吧，再见等等结束对话词汇时，则返回终止对话，如果用户说重新开始，则返回重新开始,当识别出是敏感词汇时，则返回敏感词",
                            },
                            "domain":  {
                                "type": "string",
                                "default": "对话",
                                "description": "意图域：，默认为对话",
                            }
                        },
                            "required": ["intent","domain"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "xiaoling",
                    "description": "自我介绍",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "intent": {
                                "type": "string",
                                "enum": ["自我介绍"],
                                "description": "自我介绍意图，比如用户说你是谁，你能干嘛，介绍下自己等等自我介绍词汇时，则返回自我介绍",
                            }
                        },
                            "required": ["intent"],
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "other",
                    "description": "其他，当用户输入的信息以上函数都不匹配时，或者上面的设备域 domain 域没有匹配到任何设备时，则返回其他",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "intent": {
                                "type": "string",
                                "default": "other",
                                "description": "其他，当用户输入的信息以上函数都不匹配时，或者上面的设备域 domain 域没有匹配到任何设备时，则返回其他",
                            }
                        },
                            "required": ["intent"],
                    },
                },
            },
        ]