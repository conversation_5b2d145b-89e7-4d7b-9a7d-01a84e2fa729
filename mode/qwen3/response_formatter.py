from entity.config import Config
def process_field(k, v):
    """
    通用字段处理器，用于根据字段名和值进行统一转换
    """
    if k == "room" and v == "默认":
        return None
    if k == "pos" and v in ("伦敦", "默认"):
        return None
    if k == "device" and v == "":
        return None
    if k == "value" and v in ("", None, "0"):
        return None
    # 处理带"速"风速值，如"低速" → 低
    if k == "value" and v.endswith("速"):
        return v.replace("速", "")

    # 处理带"k"单位的数值，如"2k" → 2000
    if k == "value" and isinstance(v, str) and v.lower().endswith("k"):
        return v[:-1]  # 去掉最后的 'k'
        
    # 其他自定义规则（如"度"等）可继续添加
    if k == "value" and v.endswith("度"):
        return v.replace("度", "")
    
    return v


def parse_and_reformat(item_str):
    """
    根据内容判断是否为 getWeather 或 getLiving 的响应，并按需格式化
    """

    # 判断是否是 getWeather 或 getLiving 的意图
    if "domain：天气" in item_str:
        return _reformat_weather_or_living(item_str)
    elif "domain：生活指数" in item_str:
        return _reformat_weather_or_living(item_str)
    elif "intent：other"  in item_str or   "intent：其他"  in item_str:
        return _reformat_other()
    else:
        # 其他类型不做处理，直接返回原字符串
        return item_str

def _reformat_other():
      return "NOT_SUPPORTED"

def device_not_found(deviceName:str):
    return "DEVICE_NOT_FOUND"+"&"+deviceName

def _reformat_weather_or_living(item_str):
    parts = [p.strip() for p in item_str.split("；")]
    data = {}
    for part in parts:
        if "：" not in part:
            continue
        key, value = part.split("：", 1)
        data[key] = value

    new_parts = []
    for key, value in data.items():
        if key == "intent" or key == "pos":
            new_parts.append(f"{key}：{value}")
        elif key == "unit":
            if value == "timeRange":
                time_range = data.get("offset", "")
                new_parts.append(f"timeRange：【{time_range}】")
            elif value in ["year", "month", "day", "week", "hour", "minute", "second"]:
                offset = data.get("offset", "0")
                # 清理 offset 的括号和单位前缀（如 "week:"）
                if offset.startswith("(") and offset.endswith(")"):
                    offset = offset[1:-1].replace(f"{value}:", "")
                
                # 特殊处理 week 单位
                if value == "week":
                    try:
                        # 解析偏移量（如 "+1" -> 1）
                        n = int(offset.lstrip('+'))
                        # 计算日期范围：起始 day:+0，结束 day:+(n*7-1)
                        start_day = "+0"
                        end_day = f"+{(n * 7) - 1}"
                        new_parts.append(f"timeRange：【（day：{start_day}）～（day：{end_day}）】")
                    except ValueError:
                        # 若 offset 非数字，保留原值
                        new_parts.append(f"{value}：{offset}")
                else:
                    # 非 week 单位沿用原逻辑
                    if offset.lstrip('-').isdigit():
                        offset = f"+{offset}" if not offset.startswith('-') else offset
                    new_parts.append(f"{value}：{offset}")
        # 其他字段保留
        elif key not in ["unit", "offset"]:
            new_parts.append(f"{key}：{value}")
    return "；".join(new_parts)
def format_response(parsed_objs,query:str,config:Config):
    """
    格式化响应输出，支持 dict/list 类型输入
    :param parsed_objs: 解析后的对象（dict 或 list）
    :param domain_list: 支持的领域列表
    :param device_names: 支持的设备名列表
    :return: 格式化后的响应
    """
    if not parsed_objs:
        # 记录调试信息
        from flask import current_app
        current_app.logger.warning(f"AI模型返回的解析结果为空，query: '{query}'")
        # 返回特殊标记，让main.py处理回退逻辑
        return {
            "code": "AI_EMPTY_RESULT",
            "msg": "AI模型返回空结果",
            "data": {
                "query": query
            }
        }
    domain_list = config.DEVICE_LIST
    device_names  = config.device_names
    devices = config.devices
    space = config.space
    if domain_list is None:
        domain_list = []
    if device_names is None:
        device_names = []

    # 默认支持的系统领域
    system_domains = {"场景", "天气", "生活指数", "闲聊", "对话","all"}
    domain_set = set(domain_list) | system_domains

    try:
        response_list = []

        # 统一转换为 list 处理
        items = parsed_objs if isinstance(parsed_objs, list) else [parsed_objs]

        for obj in items:
            if not isinstance(obj, dict):
                response_list.append(str(obj))
                continue

            # 过滤无效字段
            filtered_obj = {
                k: process_field(k, v)
                for k, v in obj.items()
                if process_field(k, v) is not None
            }

            domain = filtered_obj.get("domain")
            device_name = filtered_obj.get("device")

            # 域名检查
            if domain and domain not in domain_set:
                response_list.append(_reformat_other())
                continue
            old_list = ['昨天', '前天', '过去']

            if domain in ["天气", "生活指数"] and  any(old_day in query for old_day in old_list):
                 filtered_obj["offset"] = "-1"
                 filtered_obj["unit"] = "day"

            # 设备名检查
            if device_name and device_name == domain:
                filtered_obj["device"] = ""
                device_name = None

            # if device_name and device_name not in device_names:
            #     response_list.append(device_not_found(device_name))
            #     continue
            ## 设备名称若为开关，特殊处理，直接走打开开关
            for  device in devices:
                if device["name"] == device_name and "开关" not  in device_name:
                    old_intent = filtered_obj["intent"].replace(filtered_obj["domain"], "")
                    if filtered_obj["intent"] in["打开插座", "关闭插座","打开开关","关闭开关","打开灯","关闭灯","打开窗帘","关闭窗帘","暂停窗帘","打开通断器","关闭通断器","打开安防","关闭安防","打开空调","关闭空调","打开新风","关闭新风","打开杀菌","关闭杀菌","打开地暖","关闭地暖"]:
                        filtered_obj["domain"] =  device["intentDomainName"]
                        filtered_obj["intent"] = old_intent+ device["intentDomainName"]

            room = filtered_obj.get("room", None)
            device = filtered_obj.get("device", None)
            if device and room and room in device and device not in device_names:
                device_name = filtered_obj["device"].replace(room, "")
                domain = filtered_obj.get("domain",None)
                if domain and  (device_name == domain or "的"+domain == device_name):
                    filtered_obj["device"] = None
                else:
                    filtered_obj["device"] = device_name
            elif device and device not in device_names:
                rooms = [word for word in space if word in device]
                if rooms:
                    filtered_obj["device"] = filtered_obj["device"].replace(rooms[0], "")
                    filtered_obj["room"] = rooms[0]
            if "窗帘电机" in query and  (filtered_obj.get("device", None) is not None and "窗帘" == filtered_obj["device"]):
                filtered_obj["device"] = None
            intent = filtered_obj.get("intent", None)
            if intent and intent in ["调低风速", "调高风速", "设置风速"]:
                if "低速" in query or "低档" in query:
                    filtered_obj["value"] = "低"
                elif "中速" in query or "中档" in query:
                    filtered_obj["value"] = "中"
                elif "高速" in query or "高档" in query:
                    filtered_obj["value"] = "高"
                elif "自动" in query :
                    filtered_obj["value"] = "自动"
                # 转换为字符串并格式化
            item_str = "；".join(f"{k}：{v}" for k, v in filtered_obj.items() if v is not None and v != "")
            reformatted = parse_and_reformat(item_str)
            response_list.append(reformatted)

        return {
            "code": "SUCCESS",
            "msg": "成功",
            "data": {
                "response": list(set(response_list))
            }
        }

    except Exception as e:
        return {
            "code": "ERROR",
            "msg": f"识别失败: {str(e)}",
            "data": {
                "response": []
            }
        }