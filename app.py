from flask import Flask, render_template, request,jsonify
from logging.handlers import TimedRotatingFileHandler
import logging,os,sys,json
from mode.main import intent_service
from entity.msg import MsgSchema
from nlg.service import nlg_service
from config import load_config, get_config_info, get_config_manager

# 获取配置
def check_env(key: str, default):
    value = os.getenv(key)
    if value is not None:
        return value
    if default is not None:
        return default

    raise ValueError(f"{key} environment variable is not set, please set in .env")


# 加载日志配置
def log_config(app_c: Flask):
    log_path = check_env("LOG_DIR", './log')
    if not os.path.exists(log_path):
        os.makedirs(log_path)

    # 清除所有现有的处理器
    app_c.logger.handlers.clear()

    app_c.logger.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s [%(levelname)s] %(module)s.%(funcName)s: %(message)s")

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    app_c.logger.addHandler(console_handler)

    # 文件处理器（使用基本的FileHandler避免权限问题）
    try:
        file_handler = logging.FileHandler(f'{log_path}/xiaoling.log', encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)
        app_c.logger.addHandler(file_handler)
    except Exception as e:
        print(f"警告：无法创建文件日志处理器: {e}")

    # 确保不传播到根日志器
    app_c.logger.propagate = False


app = Flask(__name__)
log_config(app)

@app.route('/')
def hello():
    return "hello world"



@app.route('/send_msg', methods=['POST'])
def send_msg():
    """
    发送消息
    """
    schema = MsgSchema()
    #通过 request.get_json() 获取请求体中的 JSON 数据，并用 schema.load 进行校验和转换。如果数据格式不对或校验失败，会抛出异常。
    try:
      data =  schema.load(request.get_json())
    
    except Exception as e:
        app.logger.error(f'发生异常 {e}')
        return jsonify({
            "code": "ERROR",
            "msg": str(e),
            "data": None
        }), 500
    #将数据传递给 intent_service().send_msg 进行业务处理，获取响应内容。
    responses = intent_service().send_msg(data)
    return responses, 200

@app.route('/parse_msg', methods=['POST'])
def parse_msg():
    data = request.get_json()
    app.logger.info("parse_msg消息:"+json.dumps(data, indent=2, ensure_ascii=False))
    responses = nlg_service().parse_msg(data.get("content"),data.get("query"))
    return responses, 200

@app.route('/config/reload', methods=['POST'])
def reload_config():
    """
    重新加载配置
    """
    try:
        config_manager = get_config_manager()
        success = config_manager.load_config(force_reload=True)

        if success:
            config_info = get_config_info()
            app.logger.info(f"配置重新加载成功 - 模式类型: {config_info['mode_type']}, 模型: {config_info['model']}")
            return jsonify({
                "code": "SUCCESS",
                "msg": "配置重新加载成功",
                "data": config_info
            }), 200
        else:
            return jsonify({
                "code": "ERROR",
                "msg": "配置重新加载失败",
                "data": None
            }), 500

    except Exception as e:
        app.logger.error(f'配置重新加载异常: {e}')
        return jsonify({
            "code": "ERROR",
            "msg": str(e),
            "data": None
        }), 500

@app.route('/config/info', methods=['GET'])
def get_current_config():
    """
    获取当前配置信息
    """
    try:
        config_info = get_config_info()
        return jsonify({
            "code": "SUCCESS",
            "msg": "获取配置信息成功",
            "data": config_info
        }), 200
    except Exception as e:
        app.logger.error(f'获取配置信息异常: {e}')
        return jsonify({
            "code": "ERROR",
            "msg": str(e),
            "data": None
        }), 500

# app.py
@app.errorhandler(Exception)
def handle_exception(e):
    app.logger.error(f"发生异常: {e}", exc_info=True)
    return jsonify({
        "code": "ERROR",
        "msg": str(e),
        "data": None
    }), 500

if  __name__ == '__main__':
    # 使用统一配置管理器加载配置（强制重新加载）
    if load_config(force_reload=True):
        config_info = get_config_info()
        print(f"配置加载完成 - 模式类型: {config_info['mode_type']}, 模型: {config_info['model']}")
    else:
        print("配置加载失败，使用默认配置")

    app.run(host='0.0.0.0',port=8578 ,debug=True, use_reloader=False)