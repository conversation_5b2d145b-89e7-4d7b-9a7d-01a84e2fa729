#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复transformers库兼容性问题
解决 "argument of type 'NoneType' is not iterable" 错误
"""

from transformers import modeling_utils

# 检查并修复 ALL_PARALLEL_STYLES 问题
if not hasattr(modeling_utils, "ALL_PARALLEL_STYLES"):
    modeling_utils.ALL_PARALLEL_STYLES = ["tp", "none", "colwise", "rowwise"]
elif modeling_utils.ALL_PARALLEL_STYLES is None:
    modeling_utils.ALL_PARALLEL_STYLES = ["tp", "none", "colwise", "rowwise"]