from nlg.ds.llm_processor import LLMProcessor
from nlg.hw.hw_llm_processor  import HwLLMProcessor
from nlg.qwen3.qw_llm_processor import QwLLMProcessor
from nlg.local.local_llm_processor import LocalLLMProcessor
import json,os
from nlg.ds import system_prompt
from nlg.hw  import hw_system_prompt
from flask import current_app
from entity.config import Config
class nlg_service:
    def __init__(self):
        self.config = Config
        # 不再在每次初始化时重新加载配置
        # 配置应该在应用启动时统一加载
    def parse_msg(self,content: str,query: str):
        repr =  content.replace("\n","").replace("好的","");
        if repr == '' and content.startswith("好的"):
            return  {
                "code": "SUCCESS",
                "msg": "成功",
                "data": "好的"
            }

        # 记录规则引擎未命中状态和模型选择信息
        current_app.logger.info("=" * 80)
        current_app.logger.info("🤖 [AI模型调用] 规则引擎未命中，启动大语言模型处理")
        current_app.logger.info(f"📝 用户输入: {content}")
        current_app.logger.info(f"🔍 查询内容: {query}")

        # 检查是否使用本地模型
        use_local = getattr(self.config, 'use_local_model', False)

        if use_local:
            # 使用本地微调模型
            current_app.logger.info("🏠 [模型选择] 本地微调模型")
            current_app.logger.info(f"   ├─ 选择原因: 配置驱动 (USE_LOCAL_MODEL=true)")
            current_app.logger.info(f"   ├─ 基础模型路径: {getattr(self.config, 'local_base_model_path', 'N/A')}")
            current_app.logger.info(f"   ├─ 微调模型路径: {getattr(self.config, 'local_finetuned_model_path', 'N/A')}")
            current_app.logger.info(f"   ├─ 推理设备: {getattr(self.config, 'local_model_device', 'auto')}")
            current_app.logger.info(f"   └─ 模型类型: LoRA微调模型")

            messages = [{"role": "user", "content": content+"/no_think"}]
            responses = LocalLLMProcessor().process_llm_call(messages, query)
            current_app.logger.info(f"🏠 [本地模型] 推理完成，最终结果:"+json.dumps(responses, indent=2, ensure_ascii=False))
        else:
            # 使用云端模型
            model_name_map = {
                "hw": "华为盘古",
                "qw": "通义千问",
                "ds": "DeepSeek"
            }
            model_display_name = model_name_map.get(self.config.mode_type, "自定义模型")

            current_app.logger.info("☁️  [模型选择] 云端大语言模型")
            current_app.logger.info(f"   ├─ 选择原因: 配置驱动 (USE_LOCAL_MODEL=false)")
            current_app.logger.info(f"   ├─ 模型类型: {model_display_name}")
            current_app.logger.info(f"   ├─ 模型名称: {self.config.model}")
            current_app.logger.info(f"   ├─ 服务器地址: {self.config.model_server}")
            current_app.logger.info(f"   ├─ API密钥: {'已配置' if self.config.api_key else '未配置'}")
            current_app.logger.info(f"   └─ 模式类型: {self.config.mode_type}")

            if  self.config.mode_type == "hw":
                system_content = hw_system_prompt.system_content
            else:
                system_content = system_prompt.system_content
            messages = [{"role": "system", "content": system_content}]
            messages.append({"role": "user", "content": content+"/no_think"})

            if  self.config.mode_type == "hw":
                responses =  HwLLMProcessor().process_llm_call(messages,query)
            elif self.config.mode_type == "qw":
                responses =  QwLLMProcessor().process_llm_call(messages,query)
            else:
                responses =  LLMProcessor().process_llm_call(messages,query)
            current_app.logger.info(f"☁️  [{model_display_name}] 推理完成，最终结果:"+json.dumps(responses, indent=2, ensure_ascii=False))

        current_app.logger.info("=" * 80)
        return responses
    
