#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地微调模型处理器 - 多GPU优化版本
基于inference.py实现的本地模型调用逻辑，支持多GPU并行推理
"""

import os
import json
import time
import logging
import warnings
import re
import threading
from typing import Dict, List, Optional, Tuple, Any
from concurrent.futures import ThreadPoolExecutor
import queue

import torch
import torch.nn as nn
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM
)
from peft import PeftModel
from flask import current_app
from entity.config import Config
from .output_adapter import OutputAdapter

# 修复transformers库兼容性问题
from transformers import modeling_utils
# 检查并修复 ALL_PARALLEL_STYLES 问题
if not hasattr(modeling_utils, "ALL_PARALLEL_STYLES"):
    modeling_utils.ALL_PARALLEL_STYLES = ["tp", "none", "colwise", "rowwise"]
elif modeling_utils.ALL_PARALLEL_STYLES is None:
    modeling_utils.ALL_PARALLEL_STYLES = ["tp", "none", "colwise", "rowwise"]


# 忽略一些不重要的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

logger = logging.getLogger(__name__)


class LocalLLMProcessor:
    """
    本地微调模型处理器

    基于SmartHomeAssistantV2实现，适配现有系统的调用接口
    使用单例模式避免重复加载模型
    """

    _instance = None
    _initialized = False
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.config = Config
            self.tokenizer = None
            self.model = None

            # 多GPU设置 - 从配置文件读取GPU设备
            gpu_devices_str = getattr(self.config, 'local_gpu_devices', '6,7')
            self.original_gpu_devices = [int(x.strip()) for x in gpu_devices_str.split(',')]
            self.conda_env_path = getattr(self.config, 'local_conda_env', '/opt/conda/FT')

            # 设置CUDA_VISIBLE_DEVICES环境变量（指定使用6号和7号GPU）
            os.environ['CUDA_VISIBLE_DEVICES'] = gpu_devices_str

            # 重新映射GPU设备ID（因为CUDA_VISIBLE_DEVICES会重新编号）
            self.gpu_devices = list(range(len(self.original_gpu_devices)))  # 映射为0,1,2...

            # 设置虚拟环境相关的环境变量
            if self.conda_env_path and os.path.exists(self.conda_env_path):
                # 确保使用正确的Python路径
                python_path = os.path.join(self.conda_env_path, 'bin', 'python')
                if os.path.exists(python_path):
                    os.environ['CONDA_DEFAULT_ENV'] = 'FT'
                    os.environ['CONDA_PREFIX'] = self.conda_env_path
            self.primary_device = f"cuda:{self.gpu_devices[0]}"  # 主设备
            # 启用多GPU模式（如果有多个GPU）
            self.multi_gpu = len(self.gpu_devices) > 1
            self.device = self.primary_device  # 保持兼容性
            self.current_gpu_index = 0  # 用于负载均衡的GPU索引

            # 验证GPU可用性
            self._validate_gpu_availability()
        
            # 系统提示词 - 与inference.py保持一致
            self.system_prompt = """
你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

  1. 所有输出必须是一个**标准 JSON 对象**，且包含名为 `"function_calls"` 的字段，其值为一个 JSON 数组。
  2.设备控制强制规则：
    1. **room 参数必须精确匹配**：
      - 只返回用户明确提到的位置词（如"客厅"、"卧室"）
      - 未提及位置时返回空字符串
      
    2. **禁止位置推断**：
      - 示例：用户说"打开床头灯"
        - 正确：room = "默认"
        - 错误：room = "卧室"（禁止推断）
    3. **全屋规则**：
      - 仅当用户明确说"全屋"、"所有设备"时才返回"all"
  3. 每个数组元素是一个合法的函数调用对象，结构如下：
    {{
      "name": "函数名",
      "arguments": {{                                                                                   
        "intent": "意图，来自预定义枚举列表["打开插座", "关闭插座","打开开关","关闭开关","打开灯","关闭灯","打开窗帘","关闭窗帘","暂停窗帘","打开通断器","关闭通断器","打开安防","关闭安防",
        "打开空调","关闭空调","打开新风","关闭新风","打开杀菌","关闭杀菌","打开地暖","关闭地暖","设置亮度","调高亮度","调低亮度","设置色温","调高色温","调低色温","设置开合度","调大开合度","调小开合度",
        "设置温度","调高温度","调低温度","设置风速","调高风速","调低风速","调高地暖","调低地暖","设置地暖温度","打开场景","查询限行","查询化妆指数","查询紫外线指数","查询感冒指数","查询洗车指数","查询穿衣指数",
        "查询运动指数","查询钓鱼指数","闲聊","设备数量查询","终止对话","重新开始","敏感词","自我介绍","查询空气质量&空气污染扩散指数","查询空气湿度","查询温度/体感温度","查询风速/风向","查询天气状况","查询日出/日落时间"]",
        "content":"回答用户问题，**位置处理**：使用{config.home_address}回答"附近"类问题,但不包含控制设备比如打开空调，并打开灯，不包含设备查询（设备数量/状态查询），用于闲聊对话，严格按照纯文本输出",
        "domain": "意图域，来自预定义枚举列表["插座","通断器","灯","开关","窗帘","空调","新风","地暖","场景"，"天气","生活指数","闲聊","对话",""]；若未指定，默认为'默认'",
        "value": "设置值（仅 setHighOrLow 函数需要）",
        "room": "空间位置，来自预定义枚举列表{config.space}",
        "device": "设备昵称，用于描述用户指定的设备昵称，若设备昵称和domain参数值一致，则返回空，例如设置客厅的灯1的亮度，则返回灯1，设置客厅灯2，则返回灯2，设置卧室灯，则直接不返回",
        "scene": "场景名称，来自预定义枚举列表{config.SCENE_LIST}；如离家/回家模式，开灯/关灯，打开/关闭全屋灯",
        "pos":"查询天气或生活指数的地点，默认为伦敦",
        "offset":"查询时间偏移量规则，若为具体某一天，**必须带符号**：今天 → '+0', 明天 → '+1', 后天 → '+2', 昨天 → '-1'，若为时间范围,比如未来两天或者近两天值格式必须为【（day：+1）～（day：+2）】，不能返回【+1～+2】， 比如未来三天或者近三天值格式必须为【（day：+1）～（day：+3）】，不能返回【+1～+3】，比如5月20到5月23的天气，值格式为【（month：5；day：20）～（month：5；day：23）】，本周值为+0，比如未来一周或者近一周值格式必须为【（day：+0）～（day：+6）】，不能返回【+0～+6】， 比如未来两周或者近两周值格式必须为【（day：+0）～（day：+13）】，不能返回【+0～+14】"
        "unit":"查询天气或者生活指数的时间单位,其中timeRange表示时间范围，默认为day,来自预定义枚举列表["pos", "year", "month", "day", "week", "hour", "minute", "second","timeRange"]"
      }}
    }}
  3. 必须从以下函数中选择合适的进行调用：
    - openOrClose：用于开关类操作
    - setHighOrLow：用于调节亮度、色温、风速、温度、开合度等
    - scene：用于执行场景（如回家模式）
    - getWeather：查询天气信息
    - getLiving：查询生活指数
    - queryDevice：查询设备数量或状态
    - chat：处理闲聊类对话
    - dialog：处理对话意图（终止、重新开始、敏感词）
    - xiaoling：自我介绍

  4. 参数要求：
    - intent 必须从每个函数的 enum 中选择，不能随意构造
    - domain 必须匹配函数支持的设备类型
    - room 若用户提到“全屋”、“全部”、“所有”，返回 "all"；未明确则默认为 "默认"
    - device 若和 domain 相同（如“客厅灯” -> domain: 灯, device: 空），否则返回具体设备昵称
    - value 只能是数字或数字+百分号（%），不允许带单位文字（如“最高”→max）

  5. 输出必须是纯 JSON，不带任何解释、注释、Markdown 或多余字段。
  6. 多个意图需生成多个 function_call，按语义顺序排列。

  示例输入："帮我把卧室的灯调高亮度，再打开客厅的窗帘，并启动回家模式"
  示例输出：
  {{
    "function_calls": [
      {{
        "name": "setHighOrLow",
        "arguments": {{
          "intent": "调高亮度",
          "domain": "灯",
          "value": "",
          "room": "卧室",
          "device": ""
        }}
      }},
      {{
        "name": "openOrClose",
        "arguments": {{
          "intent": "打开窗帘",
          "domain": "窗帘",
          "room": "客厅",
          "device": ""
        }}
      }},
      {{
        "name": "scene",
        "arguments": {{
          "intent": "打开场景",
          "domain": "场景",
          "scene": "回家模式"
        }}
      }}
    ]
  }}

  当前时间: {current_time}
  
  
  """

            self._model_loaded = False
            LocalLLMProcessor._initialized = True

    def _validate_gpu_availability(self):
        """验证GPU可用性"""
        if not torch.cuda.is_available():
            logger.warning("CUDA不可用，将使用CPU模式")
            self.multi_gpu = False
            self.device = "cpu"
            return

        available_gpus = torch.cuda.device_count()
        logger.info(f"检测到 {available_gpus} 个GPU设备")

        # 验证CUDA_VISIBLE_DEVICES设置后的GPU可用性
        # 注意：设置CUDA_VISIBLE_DEVICES后，PyTorch会重新编号GPU
        if available_gpus != len(self.original_gpu_devices):
            logger.warning(f"CUDA_VISIBLE_DEVICES设置后，可见GPU数量({available_gpus})与配置数量({len(self.original_gpu_devices)})不匹配")

        # 验证映射后的GPU设备是否都可用
        for i, gpu_id in enumerate(self.gpu_devices):
            if gpu_id >= available_gpus:
                logger.error(f"映射后的GPU {gpu_id} 不可用，可用GPU数量: {available_gpus}")
                self.multi_gpu = False
                self.gpu_devices = [0]  # 回退到单GPU
                self.primary_device = "cuda:0"
                break
            else:
                # 验证GPU确实可用
                try:
                    torch.cuda.get_device_properties(gpu_id)
                    logger.debug(f"GPU {self.original_gpu_devices[i]} -> cuda:{gpu_id} 验证成功")
                except Exception as e:
                    logger.error(f"GPU {gpu_id} 验证失败: {e}")
                    self.multi_gpu = False
                    self.gpu_devices = [0]
                    self.primary_device = "cuda:0"
                    break

        logger.info(f"使用GPU设备: 原始编号 {self.original_gpu_devices} -> 映射编号 {self.gpu_devices}")
        logger.info(f"主设备: {self.primary_device}")
        logger.info(f"虚拟环境: {self.conda_env_path}")
        logger.info(f"多GPU模式: {'启用' if self.multi_gpu else '禁用'}")

    def _create_device_map(self) -> Dict[str, int]:
        """
        创建多GPU设备映射
        将模型层分布到多个GPU上
        """
        if not self.multi_gpu or len(self.gpu_devices) <= 1:
            return {"": self.gpu_devices[0]}

        # 对于Qwen2-1.5B模型，有28层transformer层
        # 我们将这些层分布到可用的GPU上
        device_map = {}

        # 将embedding层放在第一个GPU上
        device_map["model.embed_tokens"] = self.gpu_devices[0]

        # 计算每个GPU应该承担的层数
        total_layers = 28  # Qwen2-1.5B的实际层数
        layers_per_gpu = total_layers // len(self.gpu_devices)
        remaining_layers = total_layers % len(self.gpu_devices)

        current_layer = 0
        layer_distribution = {}  # 记录层分布信息

        for gpu_idx, gpu_id in enumerate(self.gpu_devices):
            # 计算当前GPU的层数（剩余层数分配给前面的GPU）
            layers_for_this_gpu = layers_per_gpu
            if gpu_idx < remaining_layers:
                layers_for_this_gpu += 1

            # 分配transformer层
            start_layer = current_layer
            end_layer = current_layer + layers_for_this_gpu - 1

            for layer_idx in range(current_layer, current_layer + layers_for_this_gpu):
                device_map[f"model.layers.{layer_idx}"] = gpu_id

            layer_distribution[gpu_id] = (start_layer, end_layer)
            current_layer += layers_for_this_gpu

        # 将最后的层（norm和lm_head）放在第一个GPU上，避免tied参数问题
        device_map["model.norm"] = self.gpu_devices[0]
        device_map["lm_head"] = self.gpu_devices[0]

        # 记录详细的层分布信息
        for gpu_id, (start, end) in layer_distribution.items():
            original_gpu = self.original_gpu_devices[self.gpu_devices.index(gpu_id)]
            logger.info(f"GPU {original_gpu} (映射为cuda:{gpu_id}) 负责第 {start}-{end} 层 ({end-start+1}层)")

        logger.info(f"embedding、norm和lm_head层在GPU {self.original_gpu_devices[0]} (cuda:{self.gpu_devices[0]})")

        return device_map

    def _select_best_gpu(self) -> int:
        """
        从可用GPU中选择内存使用最少的GPU
        """
        best_gpu = self.gpu_devices[0]
        min_memory_usage = float('inf')

        for gpu_id in self.gpu_devices:
            if torch.cuda.is_available() and gpu_id < torch.cuda.device_count():
                # 获取GPU内存使用情况
                cached_memory = torch.cuda.memory_reserved(gpu_id)
                if cached_memory < min_memory_usage:
                    min_memory_usage = cached_memory
                    best_gpu = gpu_id

        return best_gpu

    def _get_next_gpu_for_inference(self) -> int:
        """
        获取下一个用于推理的GPU（负载均衡）
        """
        if len(self.gpu_devices) == 1:
            return self.gpu_devices[0]

        # 简单的轮询策略
        gpu_id = self.gpu_devices[self.current_gpu_index]
        self.current_gpu_index = (self.current_gpu_index + 1) % len(self.gpu_devices)
        return gpu_id

    def _get_best_model_for_inference(self):
        """
        获取最佳的模型和GPU进行推理（负载均衡）
        """
        if not self.use_model_replicas or len(self.gpu_devices) == 1:
            return self.model_replicas[0], 0

        # 使用轮询策略选择GPU
        gpu_idx = self._get_next_gpu_for_inference()
        actual_gpu_idx = self.gpu_devices.index(gpu_idx)

        return self.model_replicas[0], actual_gpu_idx

    def _setup_pipeline_parallel(self):
        """设置模型并行（Pipeline Parallel）"""
        logger.info("开始设置模型并行...")

        # 获取模型的transformer层
        if hasattr(self.model, 'base_model') and hasattr(self.model.base_model, 'model'):
            base_model = self.model.base_model.model
        else:
            raise ValueError("无法访问基础模型结构")

        if not hasattr(base_model, 'layers'):
            raise ValueError("模型没有layers属性，无法进行模型并行")

        total_layers = len(base_model.layers)
        num_gpus = len(self.gpu_devices)
        layers_per_gpu = total_layers // num_gpus

        logger.info(f"总层数: {total_layers}, GPU数量: {num_gpus}, 每GPU层数: {layers_per_gpu}")

        # 分配层到不同GPU
        for gpu_idx, gpu_id in enumerate(self.gpu_devices):
            start_layer = gpu_idx * layers_per_gpu
            if gpu_idx == num_gpus - 1:  # 最后一个GPU处理剩余的层
                end_layer = total_layers
            else:
                end_layer = (gpu_idx + 1) * layers_per_gpu

            # 将指定层移动到对应GPU
            for layer_idx in range(start_layer, end_layer):
                if layer_idx < total_layers:
                    base_model.layers[layer_idx] = base_model.layers[layer_idx].to(f"cuda:{gpu_id}")
                    logger.debug(f"层 {layer_idx} -> GPU {self.original_gpu_devices[gpu_idx]} (cuda:{gpu_id})")

        # embedding和输出层保持在第一个GPU
        if hasattr(base_model, 'embed_tokens'):
            base_model.embed_tokens = base_model.embed_tokens.to(f"cuda:{self.gpu_devices[0]}")
        if hasattr(base_model, 'norm'):
            base_model.norm = base_model.norm.to(f"cuda:{self.gpu_devices[0]}")
        if hasattr(self.model.base_model, 'lm_head'):
            self.model.base_model.lm_head = self.model.base_model.lm_head.to(f"cuda:{self.gpu_devices[0]}")

        logger.info("✅ 模型并行设置完成")

        # 记录层分布
        self.layer_distribution = {}
        for gpu_idx, gpu_id in enumerate(self.gpu_devices):
            start_layer = gpu_idx * layers_per_gpu
            if gpu_idx == num_gpus - 1:
                end_layer = total_layers
            else:
                end_layer = (gpu_idx + 1) * layers_per_gpu
            self.layer_distribution[gpu_id] = (start_layer, min(end_layer, total_layers))

        # 显示分布信息
        for gpu_id, (start, end) in self.layer_distribution.items():
            original_gpu = self.original_gpu_devices[self.gpu_devices.index(gpu_id)]
            logger.info(f"GPU {original_gpu} (cuda:{gpu_id}): 层 {start}-{end-1} ({end-start}层)")

    def _pipeline_forward(self, input_ids, attention_mask=None):
        """模型并行的前向传播"""
        if not hasattr(self, 'layer_distribution'):
            raise ValueError("模型并行未正确设置")

        # 获取基础模型
        base_model = self.model.base_model.model

        # 1. Embedding层（在第一个GPU上）
        device_0 = f"cuda:{self.gpu_devices[0]}"
        input_ids = input_ids.to(device_0)
        if attention_mask is not None:
            attention_mask = attention_mask.to(device_0)

        # 获取embeddings
        hidden_states = base_model.embed_tokens(input_ids)

        # 2. 逐层前向传播，跨GPU传递
        for gpu_idx, gpu_id in enumerate(self.gpu_devices):
            device = f"cuda:{gpu_id}"
            start_layer, end_layer = self.layer_distribution[gpu_id]

            # 将hidden_states移动到当前GPU
            hidden_states = hidden_states.to(device)
            if attention_mask is not None:
                attention_mask = attention_mask.to(device)

            # 在当前GPU上执行层计算
            for layer_idx in range(start_layer, end_layer):
                if layer_idx < len(base_model.layers):
                    layer = base_model.layers[layer_idx]
                    hidden_states = layer(hidden_states, attention_mask=attention_mask)[0]

        # 3. 最终处理（在第一个GPU上）
        hidden_states = hidden_states.to(device_0)
        if attention_mask is not None:
            attention_mask = attention_mask.to(device_0)

        # norm层
        if hasattr(base_model, 'norm'):
            hidden_states = base_model.norm(hidden_states)

        # lm_head
        if hasattr(self.model.base_model, 'lm_head'):
            logits = self.model.base_model.lm_head(hidden_states)
        else:
            logits = hidden_states

        return logits

    def get_gpu_memory_info(self) -> Dict[str, Dict[str, float]]:
        """获取GPU内存使用信息"""
        memory_info = {}

        for gpu_id in self.gpu_devices:
            if torch.cuda.is_available():
                # 获取GPU内存信息（以GB为单位）
                total_memory = torch.cuda.get_device_properties(gpu_id).total_memory / (1024**3)
                allocated_memory = torch.cuda.memory_allocated(gpu_id) / (1024**3)
                cached_memory = torch.cuda.memory_reserved(gpu_id) / (1024**3)
                free_memory = total_memory - cached_memory

                memory_info[f"GPU_{gpu_id}"] = {
                    "total_gb": round(total_memory, 2),
                    "allocated_gb": round(allocated_memory, 2),
                    "cached_gb": round(cached_memory, 2),
                    "free_gb": round(free_memory, 2),
                    "utilization_percent": round((cached_memory / total_memory) * 100, 1)
                }

        return memory_info

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = {
            "gpu_config": {
                "available_gpus": self.gpu_devices,
                "primary_device": self.primary_device,
                "multi_gpu_enabled": self.multi_gpu,
                "current_device": self.device
            },
            "memory_info": self.get_gpu_memory_info(),
            "model_loaded": self._model_loaded
        }

        # 添加性能建议
        recommendations = []
        memory_info = stats["memory_info"]

        for gpu_name, info in memory_info.items():
            utilization = info.get("utilization_percent", 0)
            if utilization > 90:
                recommendations.append(f"{gpu_name} 内存使用率过高 ({utilization:.1f}%)")
            elif utilization < 10:
                recommendations.append(f"{gpu_name} 内存使用率较低 ({utilization:.1f}%)，可考虑负载均衡")

        if len(self.gpu_devices) > 1 and not self.multi_gpu:
            recommendations.append("检测到多个GPU但未启用多GPU模式，考虑启用以提升性能")

        stats["recommendations"] = recommendations
        return stats
        
    def _load_model(self) -> bool:
        """加载本地微调模型"""
        if self._model_loaded:
            return True

        # 使用锁确保模型加载的线程安全
        with self._lock:
            # 双重检查锁定模式
            if self._model_loaded:
                return True

            try:
                base_model_path = getattr(self.config, 'local_base_model_path', '')
                finetuned_model_path = getattr(self.config, 'local_finetuned_model_path', '')
                
                if not base_model_path or not finetuned_model_path:
                    logger.error("本地模型路径未配置")
                    return False
                    
                # 检查路径是否存在
                if not os.path.exists(base_model_path):
                    logger.error(f"基础模型路径不存在: {base_model_path}")
                    return False
                if not os.path.exists(finetuned_model_path):
                    logger.error(f"微调模型路径不存在: {finetuned_model_path}")
                    return False
                
                logger.info("开始加载本地微调模型...")
                
                # 加载tokenizer
                logger.info("正在加载tokenizer...")
                self.tokenizer = AutoTokenizer.from_pretrained(
                    base_model_path,
                    use_fast=False,
                    trust_remote_code=True,
                    padding_side="left"
                )
                
                # 设置pad_token
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token
                
                # 加载基础模型
                logger.info("正在加载基础模型...")
                logger.info(f"基础模型路径: {base_model_path}")
                logger.info(f"CUDA可用: {torch.cuda.is_available()}")
                logger.info(f"使用GPU设备: {self.gpu_devices}")
                logger.info(f"主设备: {self.primary_device}")

                # 统一使用单GPU加载，然后用DataParallel包装
                selected_gpu = self._select_best_gpu()
                original_gpu = self.original_gpu_devices[self.gpu_devices.index(selected_gpu)]
                logger.info(f"加载到主GPU {original_gpu} (映射为cuda:{selected_gpu})")

                base_model = AutoModelForCausalLM.from_pretrained(
                    base_model_path,
                    torch_dtype=torch.float16,  # 使用float16提升速度
                    trust_remote_code=True,
                    device_map={"": selected_gpu},  # 先加载到单GPU
                    low_cpu_mem_usage=True,
                    # attn_implementation="flash_attention_2" if hasattr(torch.nn, 'functional') else None  # 使用Flash Attention
                )
                
                # 加载LoRA权重
                logger.info("正在加载LoRA微调权重...")
                self.model = PeftModel.from_pretrained(
                    base_model,
                    finetuned_model_path,
                    torch_dtype=torch.float16  # 与基础模型保持一致
                )

                # 设置为推理模式
                self.model.eval()

                # 确保模型在正确的设备上
                self.model = self.model.to(self.primary_device)
                logger.info(f"✅ 模型已加载到主GPU: {self.primary_device}")

                # 多GPU推理优化：尝试多种并行策略
                if self.multi_gpu and len(self.gpu_devices) > 1:
                    logger.info("🚀 启用多GPU并行推理")

                    # 策略1：尝试模型并行（Pipeline Parallel）
                    try:
                        logger.info("尝试模型并行（Pipeline Parallel）...")
                        self._setup_pipeline_parallel()
                        self.parallel_mode = "pipeline"
                        logger.info("✅ 模型并行设置成功")
                    except Exception as e:
                        logger.warning(f"模型并行设置失败: {e}")

                        # 策略2：回退到DataParallel
                        logger.info("回退到DataParallel模式...")
                        self.model = torch.nn.DataParallel(
                            self.model,
                            device_ids=self.gpu_devices,
                            output_device=self.gpu_devices[0]
                        )
                        self.parallel_mode = "dataparallel"
                        logger.info("✅ DataParallel已启用")

                    logger.info(f"使用GPU: {self.gpu_devices}")
                    self.use_dataparallel = True
                else:
                    self.use_dataparallel = False
                    self.parallel_mode = "single"

                self._model_loaded = True
                logger.info("🎉 本地微调模型加载完成！")

                # 显示配置信息
                if self.multi_gpu and len(self.gpu_devices) > 1:
                    logger.info(f"🚀 多GPU推理配置:")
                    logger.info(f"   ├─ 使用GPU: {self.original_gpu_devices} (原始编号)")
                    logger.info(f"   ├─ 映射为: {self.gpu_devices} (CUDA编号)")
                    logger.info(f"   ├─ 主设备: {self.primary_device}")
                    logger.info(f"   ├─ 虚拟环境: {self.conda_env_path}")
                    logger.info(f"   ├─ 并行模式: Pipeline Parallel (模型层分布)")
                    logger.info(f"   └─ GPU数量: {len(self.gpu_devices)}个")
                else:
                    logger.info(f"🚀 单GPU推理配置:")
                    logger.info(f"   ├─ 使用GPU: {self.original_gpu_devices[0] if self.original_gpu_devices else 'N/A'}")
                    logger.info(f"   ├─ 虚拟环境: {self.conda_env_path}")
                    logger.info(f"   └─ 设备: {self.device}")

                # 显示GPU内存使用情况
                memory_info = self.get_gpu_memory_info()
                logger.info("📊 GPU内存使用情况:")
                for gpu_name, info in memory_info.items():
                    logger.info(f"   ├─ {gpu_name}: {info['allocated_gb']:.1f}GB/{info['total_gb']:.1f}GB ({info['utilization_percent']:.1f}%)")

                return True
                
            except Exception as e:
                logger.error(f"❌ 本地模型加载失败: {e}")
                import traceback
                logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
                return False

    def _verify_model_distribution(self):
        """验证模型在多GPU上的分布情况"""
        if not self.multi_gpu or not self.model:
            return

        try:
            logger.info("🔍 验证模型分布情况:")

            # 检查模型各部分的设备分布
            device_usage = {}

            # 检查embedding层
            if hasattr(self.model.base_model.model, 'embed_tokens'):
                embed_device = next(self.model.base_model.model.embed_tokens.parameters()).device
                device_usage['embed_tokens'] = str(embed_device)

            # 检查transformer层
            if hasattr(self.model.base_model.model, 'layers'):
                for i, layer in enumerate(self.model.base_model.model.layers):
                    layer_device = next(layer.parameters()).device
                    if str(layer_device) not in device_usage:
                        device_usage[str(layer_device)] = []
                    if 'layers' not in device_usage:
                        device_usage['layers'] = {}
                    device_usage['layers'][f'layer_{i}'] = str(layer_device)

            # 检查输出层
            if hasattr(self.model.base_model, 'lm_head'):
                lm_head_device = next(self.model.base_model.lm_head.parameters()).device
                device_usage['lm_head'] = str(lm_head_device)

            # 统计每个设备上的层数
            device_layer_count = {}
            if 'layers' in device_usage:
                for _, device in device_usage['layers'].items():
                    if device not in device_layer_count:
                        device_layer_count[device] = 0
                    device_layer_count[device] += 1

            # 输出分布信息
            logger.info(f"   ├─ Embedding层: {device_usage.get('embed_tokens', 'N/A')}")
            logger.info(f"   ├─ LM Head层: {device_usage.get('lm_head', 'N/A')}")
            logger.info("   ├─ Transformer层分布:")
            for device, count in device_layer_count.items():
                original_gpu = None
                if device.startswith('cuda:'):
                    gpu_idx = int(device.split(':')[1])
                    if gpu_idx < len(self.original_gpu_devices):
                        original_gpu = self.original_gpu_devices[gpu_idx]
                logger.info(f"   │   └─ {device} (GPU {original_gpu}): {count}层")

            logger.info("   └─ ✅ 模型分布验证完成")

        except Exception as e:
            logger.warning(f"模型分布验证失败: {e}")

    def _extract_json_from_response(self, response: str) -> str:
        """
        从响应中提取JSON对象
        与inference.py保持一致的提取逻辑
        """
        # 首先尝试找到assistant标记后的内容
        if "assistant\n" in response:
            response = response.split("assistant\n")[-1]
        elif "<|im_start|>assistant\n" in response:
            response = response.split("<|im_start|>assistant\n")[-1]
        
        # 移除结束标记
        if "<|im_end|>" in response:
            response = response.split("<|im_end|>")[0]
        
        response = response.strip()
        
        # 使用正则表达式找到第一个完整的JSON对象
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, response)
        
        if matches:
            # 返回第一个匹配的JSON
            return matches[0]
        
        # 如果正则表达式失败，尝试手动解析
        if response.startswith("{"):
            brace_count = 0
            json_end = -1
            in_string = False
            escape_next = False
            
            for i, char in enumerate(response):
                if escape_next:
                    escape_next = False
                    continue
                    
                if char == "\\" and in_string:
                    escape_next = True
                    continue
                    
                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue
                    
                if not in_string:
                    if char == "{":
                        brace_count += 1
                    elif char == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break
            
            if json_end > 0:
                return response[:json_end]
        
        return response

    def predict(
        self,
        user_input: str,
        max_new_tokens: int = 256,  
        temperature: float = 0.1,  # 参考代码的温度设置
        do_sample: bool = False  # 参考代码的采样设置
    ) -> Tuple[str, float]:
        """进行推理预测（重构版本，参考更好的推理方案）"""
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("模型未加载，请先调用_load_model()方法")

        try:
            start_time = time.time()

            # 系统提示词 - 恢复到简单格式
            system_prompt = """你是智能家居助手，根据用户指令输出JSON格式的函数调用。

输出格式：{"name": "函数名", "arguments": {"intent": "意图", "domain": "设备类型", "room": "房间", "device": "设备名", "value": "数值"}}

支持的函数：
- openOrClose: 开关控制（开灯、关灯、开空调等）
- setHighOrLow: 调节控制（调温度、调亮度等）
- scene: 场景控制（回家模式、睡眠模式等）
- getWeather: 天气查询
- chat: 闲聊对话

示例：
用户："卧室空调调到27度"
输出：{"name": "setHighOrLow", "arguments": {"intent": "调高温度", "domain": "空调", "room": "卧室", "device": "", "value": "27"}}

用户："打开客厅灯"
输出：{"name": "openOrClose", "arguments": {"intent": "打开灯", "domain": "灯", "room": "客厅", "device": "", "value": ""}}

只输出JSON，不要其他内容。"""

            # 构建messages格式，使用chat_template
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input}
            ]

            # 使用tokenizer的chat_template构建输入文本
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )

            # 编码输入
            model_inputs = self.tokenizer([text], return_tensors="pt")

            # 将输入移动到主设备
            model_inputs = model_inputs.to(self.primary_device)

            # 选择使用的模型
            selected_model = self.model

            # 优化的生成响应
            with torch.no_grad():
                # 确保模型在推理模式
                selected_model.eval()

                # 处理DataParallel包装的模型
                model_to_use = selected_model.module if hasattr(selected_model, 'module') else selected_model

                generated_ids = model_to_use.generate(
                    model_inputs.input_ids,
                    attention_mask=model_inputs.attention_mask,  # 添加attention_mask
                    max_new_tokens=max_new_tokens,
                    do_sample=do_sample,
                    temperature=temperature,
                    pad_token_id=self.tokenizer.eos_token_id,
                    use_cache=True,  # 启用KV缓存
                    num_beams=1,  # 使用贪婪搜索，更快
                    early_stopping=True  # 早停
                )

            # 只解码新生成的token部分
            generated_ids = [
                output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]

            response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]

            # 提取JSON
            json_response = self._extract_json_from_response(response)

            inference_time = time.time() - start_time

            logger.info(f"推理完成，耗时: {inference_time:.3f}秒")
            logger.info(f"原始响应: {response}")
            logger.info(f"提取的JSON: {json_response}")
            return json_response.strip(), inference_time

        except Exception as e:
            logger.error(f"推理失败: {e}")
            return f"ERROR: {str(e)}", 0.0

    def predict_batch(
        self,
        user_inputs: List[str],
        max_new_tokens: int = 128,
        temperature: float = 0.1,
        do_sample: bool = False,
        batch_size: int = None
    ) -> List[Tuple[str, float]]:
        """批处理推理预测（多GPU优化版本）"""
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("模型未加载，请先调用_load_model()方法")

        if not user_inputs:
            return []

        try:
            start_time = time.time()
            batch_size = len(user_inputs)

            # 系统提示词
            system_prompt = """你是智能家居助手，根据用户指令输出JSON格式的函数调用。

输出格式：{"name": "函数名", "arguments": {"intent": "意图", "domain": "设备类型", "room": "房间", "device": "设备名", "value": "数值"}}

支持的函数：
- openOrClose: 开关控制（开灯、关灯、开空调等）
- setHighOrLow: 调节控制（调温度、调亮度等）
- scene: 场景控制（回家模式、睡眠模式等）
- getWeather: 天气查询
- chat: 闲聊对话

只输出JSON，不要其他内容。"""

            # 构建批量messages
            all_messages = []
            for user_input in user_inputs:
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_input}
                ]
                all_messages.append(messages)

            # 使用tokenizer的chat_template构建批量输入文本
            texts = []
            for messages in all_messages:
                text = self.tokenizer.apply_chat_template(
                    messages,
                    tokenize=False,
                    add_generation_prompt=True
                )
                texts.append(text)

            # 批量编码输入
            model_inputs = self.tokenizer(
                texts,
                return_tensors="pt",
                padding=True,
                truncation=True
            ).to(self.primary_device)

            # 批量生成响应
            with torch.no_grad():
                # 处理DataParallel包装的模型
                model_to_use = self.model.module if hasattr(self.model, 'module') else self.model

                generated_ids = model_to_use.generate(
                    model_inputs.input_ids,
                    attention_mask=model_inputs.attention_mask,
                    max_new_tokens=max_new_tokens,
                    do_sample=do_sample,
                    temperature=temperature,
                    pad_token_id=self.tokenizer.eos_token_id
                )

            # 解码批量响应
            results = []
            for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids):
                # 只解码新生成的token部分
                new_tokens = output_ids[len(input_ids):]
                response = self.tokenizer.decode(new_tokens, skip_special_tokens=True)

                # 提取JSON
                json_response = self._extract_json_from_response(response)
                results.append((json_response.strip(), 0.0))  # 单个推理时间设为0

            total_inference_time = time.time() - start_time
            avg_inference_time = total_inference_time / batch_size

            logger.info(f"批处理推理完成，批量大小: {batch_size}, 总耗时: {total_inference_time:.3f}秒, 平均耗时: {avg_inference_time:.3f}秒/个")

            # 更新结果中的推理时间
            results = [(json_resp, avg_inference_time) for json_resp, _ in results]
            return results

        except Exception as e:
            logger.error(f"批处理推理失败: {e}")
            # 返回错误结果
            error_result = f"ERROR: {str(e)}"
            return [(error_result, 0.0) for _ in user_inputs]

    def predict_multi_gpu_optimized(
        self,
        user_input: str,
        max_new_tokens: int = 128,
        temperature: float = 0.1,
        do_sample: bool = False,
        use_batch_simulation: bool = True
    ) -> Tuple[str, float]:
        """
        多GPU优化推理方法
        通过模拟批处理来更好地利用多GPU
        """
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("模型未加载，请先调用_load_model()方法")

        try:
            start_time = time.time()

            if use_batch_simulation and self.use_dataparallel and len(self.gpu_devices) > 1:
                # 模拟批处理：复制输入到批次中以更好地利用多GPU
                batch_size = len(self.gpu_devices)  # 每个GPU一个样本
                logger.debug(f"使用批处理模拟，批次大小: {batch_size}")

                # 创建批次输入（重复相同的输入）
                batch_inputs = [user_input] * batch_size

                # 使用批处理推理
                batch_results = self.predict_batch(
                    batch_inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    do_sample=do_sample,
                    batch_size=batch_size
                )

                # 返回第一个结果（所有结果应该相同）
                response, _ = batch_results[0]
                inference_time = time.time() - start_time

                logger.info(f"多GPU批处理推理完成，耗时: {inference_time:.3f}秒")
                return response, inference_time
            else:
                # 回退到单样本推理
                return self.predict(user_input, max_new_tokens, temperature, do_sample)

        except Exception as e:
            logger.error(f"多GPU优化推理失败: {e}")
            return f"ERROR: {str(e)}", 0.0

    def process_llm_call(self, messages: List[Dict], query: str) -> Dict[str, Any]:
        """
        处理LLM调用的主要方法
        适配现有系统的调用接口
        """
        try:
            # 确保模型已加载
            if not self._load_model():
                return {
                    "code": "ERROR",
                    "msg": "本地模型处理失败: 本地模型加载失败",
                    "data": ""
                }

            # 从messages中提取用户输入
            user_input = query
            if messages and len(messages) > 0:
                # 查找用户消息
                for msg in messages:
                    if msg.get("role") == "user":
                        user_input = msg.get("content", query)
                        break

            # 进行推理（优先使用单个推理，保持接口兼容性）
            response, inference_time = self.predict(user_input)

            # 记录GPU内存使用情况
            if logger.isEnabledFor(logging.DEBUG):
                memory_info = self.get_gpu_memory_info()
                logger.debug(f"GPU内存使用情况: {memory_info}")

            if response.startswith("ERROR:"):
                return {
                    "code": "ERROR",
                    "msg": f"本地模型处理失败: {response}",
                    "data": ""
                }

            # 使用OutputAdapter处理输出
            try:
                adapter = OutputAdapter()
                processed_response = adapter.process_response(response)

                return {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": {
                        "response": processed_response,
                        "inference_time": inference_time
                    }
                }
            except Exception as e:
                logger.error(f"输出适配失败: {e}")
                # 如果适配失败，返回原始响应
                return {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": {
                        "response": [response],
                        "inference_time": inference_time
                    }
                }

        except Exception as e:
            logger.error(f"本地模型处理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {
                "code": "ERROR",
                "msg": f"本地模型处理失败: {str(e)}",
                "data": ""
            }

    def predict_multi_request_parallel(
        self,
        user_inputs: List[str],
        max_new_tokens: int = 128,
        temperature: float = 0.1,
        do_sample: bool = False
    ) -> List[Tuple[str, float]]:
        """
        多请求并行推理：为每个GPU创建独立的模型副本
        真正实现多GPU并行处理
        """
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("模型未加载，请先调用_load_model()方法")

        if not user_inputs:
            return []

        try:
            start_time = time.time()
            num_requests = len(user_inputs)
            num_gpus = len(self.gpu_devices)

            logger.info(f"🚀 多请求并行推理: {num_requests}个请求，{num_gpus}个GPU")

            # 创建多个模型副本（每个GPU一个）
            if not hasattr(self, 'model_replicas_parallel'):
                logger.info("创建多GPU模型副本...")
                self._create_model_replicas()

            # 将请求分配到不同GPU
            gpu_requests = [[] for _ in range(num_gpus)]
            for i, user_input in enumerate(user_inputs):
                gpu_idx = i % num_gpus
                gpu_requests[gpu_idx].append((i, user_input))

            # 并行处理
            import threading

            results = [None] * num_requests

            def process_on_gpu(gpu_idx, requests):
                """在指定GPU上处理请求"""
                if not requests:
                    return

                gpu_id = self.gpu_devices[gpu_idx]
                model_replica = self.model_replicas_parallel[gpu_idx]

                logger.debug(f"GPU {self.original_gpu_devices[gpu_idx]} 处理 {len(requests)} 个请求")

                for req_idx, user_input in requests:
                    try:
                        # 单独推理
                        response, inference_time = self._single_inference_on_gpu(
                            model_replica, user_input, gpu_id, max_new_tokens, temperature, do_sample
                        )
                        results[req_idx] = (response, inference_time)
                    except Exception as e:
                        logger.error(f"GPU {gpu_idx} 处理请求 {req_idx} 失败: {e}")
                        results[req_idx] = (f"ERROR: {str(e)}", 0.0)

            # 启动多线程并行处理
            threads = []
            for gpu_idx in range(num_gpus):
                if gpu_requests[gpu_idx]:  # 只为有请求的GPU创建线程
                    thread = threading.Thread(
                        target=process_on_gpu,
                        args=(gpu_idx, gpu_requests[gpu_idx])
                    )
                    threads.append(thread)
                    thread.start()

            # 等待所有线程完成
            for thread in threads:
                thread.join()

            total_time = time.time() - start_time
            avg_time = total_time / num_requests if num_requests > 0 else 0

            logger.info(f"✅ 多请求并行推理完成: 总时间 {total_time:.3f}秒, 平均 {avg_time:.3f}秒/个")

            # 确保所有结果都有值
            for i in range(num_requests):
                if results[i] is None:
                    results[i] = ("ERROR: 处理失败", 0.0)

            return results

        except Exception as e:
            logger.error(f"多请求并行推理失败: {e}")
            return [(f"ERROR: {str(e)}", 0.0) for _ in user_inputs]

    def _create_model_replicas(self):
        """为每个GPU创建模型副本"""
        logger.info("开始创建多GPU模型副本...")

        self.model_replicas_parallel = []

        # 第一个GPU使用现有模型
        self.model_replicas_parallel.append(self.model)
        logger.info(f"✅ GPU {self.original_gpu_devices[0]} 使用现有模型")

        # 为其他GPU创建轻量级副本
        for i in range(1, len(self.gpu_devices)):
            gpu_id = self.gpu_devices[i]
            logger.info(f"创建GPU {self.original_gpu_devices[i]} (cuda:{gpu_id}) 的模型副本...")

            try:
                # 简化方案：复制现有模型到新GPU
                import copy
                model_replica = copy.deepcopy(self.model)
                model_replica = model_replica.to(f"cuda:{gpu_id}")
                model_replica.eval()

                self.model_replicas_parallel.append(model_replica)
                logger.info(f"✅ GPU {self.original_gpu_devices[i]} 模型副本创建成功")

            except Exception as e:
                logger.error(f"❌ GPU {self.original_gpu_devices[i]} 模型副本创建失败: {e}")
                # 使用主模型作为回退
                self.model_replicas_parallel.append(self.model)

        logger.info(f"✅ 模型副本创建完成，共 {len(self.model_replicas_parallel)} 个副本")

    def _single_inference_on_gpu(self, model, user_input, gpu_id, max_new_tokens, temperature, do_sample):
        """在指定GPU上进行单次推理"""
        device = f"cuda:{gpu_id}"

        # 构建输入
        system_prompt = """你是智能家居助手，根据用户指令输出JSON格式的函数调用。

输出格式：{"name": "函数名", "arguments": {"intent": "意图", "domain": "设备类型", "room": "房间", "device": "设备名", "value": "数值"}}

支持的函数：
- openOrClose: 开关控制（开灯、关灯、开空调等）
- setHighOrLow: 调节控制（调温度、调亮度等）
- scene: 场景控制（回家模式、睡眠模式等）
- getWeather: 天气查询
- chat: 闲聊对话

只输出JSON，不要其他内容。"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input}
        ]

        text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )

        # 编码并移动到指定GPU
        model_inputs = self.tokenizer([text], return_tensors="pt").to(device)

        # 推理
        with torch.no_grad():
            model.eval()

            # 处理可能的DataParallel包装
            model_to_use = model.module if hasattr(model, 'module') else model

            generated_ids = model_to_use.generate(
                model_inputs.input_ids,
                attention_mask=model_inputs.attention_mask,
                max_new_tokens=max_new_tokens,
                do_sample=do_sample,
                temperature=temperature,
                pad_token_id=self.tokenizer.eos_token_id,
                use_cache=True,
                num_beams=1,
                early_stopping=True
            )

        # 解码
        generated_ids = [
            output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
        ]

        response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
        json_response = self._extract_json_from_response(response)

        return json_response.strip(), 0.0  # 单个推理时间在并行模式下不单独计算

    def predict_smart_multi_gpu(
        self,
        user_inputs: List[str],
        max_new_tokens: int = 128,
        temperature: float = 0.1,
        do_sample: bool = False
    ) -> List[Tuple[str, float]]:
        """
        智能多GPU推理：根据请求数量自动选择最优策略
        """
        if not user_inputs:
            return []

        num_requests = len(user_inputs)
        num_gpus = len(self.gpu_devices)

        logger.info(f"🧠 智能多GPU推理: {num_requests}个请求，{num_gpus}个GPU")

        # 策略选择逻辑
        if num_requests == 1:
            # 单个请求：使用标准推理
            logger.info("策略: 单个推理")
            result = self.predict(user_inputs[0], max_new_tokens, temperature, do_sample)
            return [result]

        elif num_requests <= num_gpus * 2:
            # 少量请求：使用批处理
            logger.info("策略: 批处理推理")
            return self.predict_batch(user_inputs, max_new_tokens, temperature, do_sample)

        else:
            # 大量请求：分批处理，充分利用多GPU
            logger.info("策略: 分批多GPU处理")

            # 计算最优批次大小
            optimal_batch_size = max(2, num_gpus)
            batches = [user_inputs[i:i+optimal_batch_size] for i in range(0, num_requests, optimal_batch_size)]

            all_results = []
            total_start_time = time.time()

            for i, batch in enumerate(batches):
                logger.debug(f"处理批次 {i+1}/{len(batches)}: {len(batch)}个请求")
                batch_results = self.predict_batch(batch, max_new_tokens, temperature, do_sample)
                all_results.extend(batch_results)

            total_time = time.time() - total_start_time
            avg_time = total_time / num_requests

            logger.info(f"✅ 分批处理完成: 总时间 {total_time:.3f}秒, 平均 {avg_time:.3f}秒/个")

            return all_results

    def get_multi_gpu_performance_stats(self):
        """获取多GPU性能统计"""
        stats = self.get_performance_stats()

        # 添加多GPU特定信息
        multi_gpu_stats = {
            "multi_gpu_enabled": self.multi_gpu,
            "parallel_mode": getattr(self, 'parallel_mode', 'unknown'),
            "gpu_count": len(self.gpu_devices),
            "original_gpus": self.original_gpu_devices,
            "mapped_gpus": self.gpu_devices,
            "has_model_replicas": hasattr(self, 'model_replicas_parallel'),
        }

        if hasattr(self, 'model_replicas_parallel'):
            multi_gpu_stats["replica_count"] = len(self.model_replicas_parallel)

        stats["multi_gpu_info"] = multi_gpu_stats

        # 性能建议
        recommendations = stats.get("recommendations", [])

        if self.multi_gpu:
            recommendations.extend([
                "💡 对于单个请求，多GPU无明显加速",
                "🚀 对于批量请求，使用predict_batch()获得最佳性能",
                "⚡ 对于大量请求，使用predict_smart_multi_gpu()自动优化",
                f"📊 当前配置可同时处理{len(self.gpu_devices)}个并行请求"
            ])

        stats["recommendations"] = recommendations

        return stats


