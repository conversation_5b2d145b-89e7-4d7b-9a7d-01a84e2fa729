#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地微调模型处理器
基于Qwen2.5-7B-Instruct的智能家居控制模型
"""

import os
import json
import time
import logging
import threading
import re
from typing import Dict, List, Tuple, Any

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from entity.config import Config
from .output_adapter import OutputAdapter

# 修复transformers库兼容性问题
from transformers import modeling_utils
if not hasattr(modeling_utils, "ALL_PARALLEL_STYLES") or modeling_utils.ALL_PARALLEL_STYLES is None:
    modeling_utils.ALL_PARALLEL_STYLES = ["tp", "none", "colwise", "rowwise"]

logger = logging.getLogger(__name__)


class LocalLLMProcessor:
    """
    本地微调模型处理器
    基于Qwen2.5-7B-Instruct的智能家居控制模型
    """

    _instance = None
    _initialized = False
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self.config = Config
            self.tokenizer = None
            self.model = None
            self.device = "cuda" if torch.cuda.is_available() else "cpu"

            # 设置GPU设备 - 与example.py保持一致
            gpu_devices_str = getattr(self.config, 'local_gpu_devices', '5,6')
            os.environ['CUDA_VISIBLE_DEVICES'] = gpu_devices_str

            # 系统提示词 - 与example.py保持完全一致
            self.system_prompt = """你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

1. 所有输出必须是一个**标准 JSON 对象**，且包含名为 `"function_calls"` 的字段，其值为一个 JSON 数组。

2. 设备控制强制规则：
   1. **room 参数必须精确匹配**：
      - 只返回用户明确提到的位置词（如"客厅"、"卧室"）
      - 未提及位置时返回"默认"

   2. **禁止位置推断**：
      - 示例：用户说"打开床头灯"
        - 正确：room = "默认"
        - 错误：room = "卧室"（禁止推断）

   3. **全屋规则**：
      - 仅当用户明确说"全屋"、"所有设备"时才返回"all"

3. 每个数组元素是一个合法的函数调用对象，结构如下：
   {
     "name": "函数名",
     "arguments": {
       "intent": "意图，来自预定义枚举列表[\"打开插座\", \"关闭插座\",\"打开开关\",\"关闭开关\",\"打开灯\",\"关闭灯\",\"打开窗帘\",\"关闭窗帘\",\"暂停窗帘\",\"打开通断器\",\"关闭通断器\",\"打开安防\",\"关闭安防\",\"打开空调\",\"关闭空调\",\"打开新风\",\"关闭新风\",\"打开杀菌\",\"关闭杀菌\",\"打开地暖\",\"关闭地暖\",\"设置亮度\",\"调高亮度\",\"调低亮度\",\"设置色温\",\"调高色温\",\"调低色温\",\"设置开合度\",\"调大开合度\",\"调小开合度\",\"设置温度\",\"调高温度\",\"调低温度\",\"设置风速\",\"调高风速\",\"调低风速\",\"调高地暖\",\"调低地暖\",\"设置地暖温度\",\"打开场景\",\"查询限行\",\"查询化妆指数\",\"查询紫外线指数\",\"查询感冒指数\",\"查询洗车指数\",\"查询穿衣指数\",\"查询运动指数\",\"查询钓鱼指数\",\"闲聊\",\"设备数量查询\",\"终止对话\",\"重新开始\",\"敏感词\",\"自我介绍\",\"查询空气质量&空气污染扩散指数\",\"查询空气湿度\",\"查询温度/体感温度\",\"查询风速/风向\",\"查询天气状况\",\"查询日出/日落时间\"]",
       "content": "回答用户问题，用于闲聊对话，严格按照纯文本输出",
       "domain": "意图域，来自预定义枚举列表[\"插座\",\"通断器\",\"灯\",\"开关\",\"窗帘\",\"空调\",\"新风\",\"地暖\",\"场景\",\"天气\",\"生活指数\",\"闲聊\",\"对话\",\"\"]；若未指定，默认为'默认'",
       "value": "设置值（仅 setHighOrLow 函数需要）",
       "room": "空间位置，若用户提到\"全屋\"、\"全部\"、\"所有\"，返回 \"all\"；未明确则默认为 \"默认\"",
       "device": "设备昵称，用于描述用户指定的设备昵称，若设备昵称和domain参数值一致，则返回空字符串",
       "scene": "场景名称，如离家/回家模式，开灯/关灯，打开/关闭全屋灯",
       "pos": "查询天气或生活指数的地点，默认为伦敦",
       "offset": "查询时间偏移量规则，若为具体某一天，必须带符号：今天 → '+0', 明天 → '+1', 后天 → '+2', 昨天 → '-1'",
       "unit": "查询天气或者生活指数的时间单位，默认为day，来自预定义枚举列表[\"pos\", \"year\", \"month\", \"day\", \"week\", \"hour\", \"minute\", \"second\",\"timeRange\"]"
     }
   }

4. 必须从以下函数中选择合适的进行调用：
   - openOrClose：用于开关类操作
   - setHighOrLow：用于调节亮度、色温、风速、温度、开合度等
   - scene：用于执行场景（如回家模式）
   - getWeather：查询天气信息
   - getLiving：查询生活指数
   - queryDevice：查询设备数量或状态
   - chat：处理闲聊类对话
   - dialog：处理对话意图（终止、重新开始、敏感词）
   - xiaoling：自我介绍

5. 参数要求：
   - intent 必须从预定义枚举列表中选择，不能随意构造
   - domain 必须匹配函数支持的设备类型
   - value 只能是数字或数字+百分号（%），不允许带单位文字
   - device 若和 domain 相同则返回空字符串，否则返回具体设备昵称

6. 输出必须是纯 JSON，不带任何解释、注释、Markdown 或多余字段。

7. 多个意图需生成多个 function_call，按语义顺序排列。

示例输入："帮我把卧室的灯调高亮度，再打开客厅的窗帘，并启动回家模式"
示例输出：
{
  "function_calls": [
    {
      "name": "setHighOrLow",
      "arguments": {
        "intent": "调高亮度",
        "domain": "灯",
        "value": "",
        "room": "卧室",
        "device": ""
      }
    },
    {
      "name": "openOrClose",
      "arguments": {
        "intent": "打开窗帘",
        "domain": "窗帘",
        "room": "客厅",
        "device": ""
      }
    },
    {
      "name": "scene",
      "arguments": {
        "intent": "打开场景",
        "domain": "场景",
        "scene": "回家模式"
      }
    }
  ]
}"""

            self._model_loaded = False
            LocalLLMProcessor._initialized = True

    def _load_model(self) -> bool:
        """加载本地微调模型"""
        if self._model_loaded:
            return True

        with self._lock:
            if self._model_loaded:
                return True

            try:
                base_model_path = getattr(self.config, 'local_base_model_path', '')
                finetuned_model_path = getattr(self.config, 'local_finetuned_model_path', '')

                if not base_model_path or not finetuned_model_path:
                    logger.error("本地模型路径未配置")
                    return False

                if not os.path.exists(base_model_path):
                    logger.error(f"基础模型路径不存在: {base_model_path}")
                    return False
                if not os.path.exists(finetuned_model_path):
                    logger.error(f"微调模型路径不存在: {finetuned_model_path}")
                    return False

                logger.info("开始加载本地微调模型...")

                # 加载tokenizer
                logger.info("正在加载tokenizer...")
                self.tokenizer = AutoTokenizer.from_pretrained(
                    base_model_path,
                    use_fast=False,
                    trust_remote_code=True
                )



                # 加载基础模型
                logger.info("正在加载基础模型...")
                base_model = AutoModelForCausalLM.from_pretrained(
                    base_model_path,
                    torch_dtype=torch.float16,
                    trust_remote_code=True,
                    device_map="auto",
                    low_cpu_mem_usage=True
                )

                # 加载LoRA权重
                logger.info("正在加载LoRA微调权重...")
                self.model = PeftModel.from_pretrained(
                    base_model,
                    finetuned_model_path,
                    torch_dtype=torch.float16
                )

                # 设置为推理模式
                self.model.eval()

                self._model_loaded = True
                logger.info("✅ 本地微调模型加载完成！")
                return True

            except Exception as e:
                logger.error(f"❌ 本地模型加载失败: {e}")
                import traceback
                logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
                return False

    def _extract_json_from_response(self, response: str) -> str:
        """从响应中提取JSON对象"""
        # 首先尝试找到assistant标记后的内容
        if "assistant\n" in response:
            response = response.split("assistant\n")[-1]
        elif "<|im_start|>assistant\n" in response:
            response = response.split("<|im_start|>assistant\n")[-1]

        # 移除结束标记
        if "<|im_end|>" in response:
            response = response.split("<|im_end|>")[0]

        response = response.strip()

        # 使用正则表达式找到第一个完整的JSON对象
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, response)

        if matches:
            return matches[0]

        # 如果正则表达式失败，尝试手动解析
        if response.startswith("{"):
            brace_count = 0
            json_end = -1
            in_string = False
            escape_next = False

            for i, char in enumerate(response):
                if escape_next:
                    escape_next = False
                    continue

                if char == "\\" and in_string:
                    escape_next = True
                    continue

                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue

                if not in_string:
                    if char == "{":
                        brace_count += 1
                    elif char == "}":
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break

            if json_end > 0:
                return response[:json_end]

        return response

    def predict(self, user_input: str, max_new_tokens: int = 256, do_sample: bool = False) -> Tuple[str, float]:
        """进行推理预测"""
        if self.model is None or self.tokenizer is None:
            raise RuntimeError("模型未加载，请先调用_load_model()方法")

        try:
            start_time = time.time()

            # 构建messages格式
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_input}
            ]

            # 使用tokenizer的chat_template构建输入文本
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )

            # 编码输入（显式生成attention_mask）
            model_inputs = self.tokenizer(
                [text],
                return_tensors="pt",
                padding=True,
                truncation=True
            )

            # 多GPU时将输入移到第一个参数所在设备
            if hasattr(self.model, 'hf_device_map'):
                first_param_device = next(self.model.parameters()).device
                for k in model_inputs:
                    model_inputs[k] = model_inputs[k].to(first_param_device)
            else:
                for k in model_inputs:
                    model_inputs[k] = model_inputs[k].to(self.device)

            # 确保pad_token_id与eos_token_id不同
            if self.tokenizer.pad_token_id is None or self.tokenizer.pad_token_id == self.tokenizer.eos_token_id:
                self.tokenizer.pad_token = "[PAD]"
                if self.tokenizer.pad_token_id is None:
                    self.tokenizer.pad_token_id = self.tokenizer.eos_token_id

            # 生成响应（加速推理：限制max_new_tokens并禁用采样）
            with torch.no_grad():
                generated_ids = self.model.generate(
                    input_ids=model_inputs.input_ids,
                    attention_mask=model_inputs.attention_mask,  # 显式传递attention_mask
                    max_new_tokens=max_new_tokens,  # 保持加速
                    do_sample=do_sample,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # 解码响应
            generated_ids = [
                output_ids[len(input_ids):]
                for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]
            response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]

            # 提取JSON
            json_response = self._extract_json_from_response(response)

            inference_time = time.time() - start_time
            logger.info(f"推理完成，耗时: {inference_time:.3f}秒")
            return json_response.strip(), inference_time

        except Exception as e:
            logger.error(f"推理失败: {e}")
            return f"ERROR: {str(e)}", 0.0

    def process_llm_call(self, messages: List[Dict], query: str) -> Dict[str, Any]:
        """
        处理LLM调用的主要方法
        适配现有系统的调用接口
        """
        try:
            # 确保模型已加载
            if not self._load_model():
                return {
                    "code": "ERROR",
                    "msg": "本地模型处理失败: 本地模型加载失败",
                    "data": ""
                }

            # 从messages中提取用户输入
            user_input = query
            if messages and len(messages) > 0:
                # 查找用户消息
                for msg in messages:
                    if msg.get("role") == "user":
                        user_input = msg.get("content", query)
                        break

            # 进行推理
            response, inference_time = self.predict(user_input)

            if response.startswith("ERROR:"):
                return {
                    "code": "ERROR",
                    "msg": f"本地模型处理失败: {response}",
                    "data": ""
                }

            # 使用OutputAdapter处理输出
            try:
                adapter = OutputAdapter()
                processed_response = adapter.process_response(response)

                return {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": {
                        "response": processed_response,
                        "inference_time": inference_time
                    }
                }
            except Exception as e:
                logger.error(f"输出适配失败: {e}")
                # 如果适配失败，返回原始响应
                return {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": {
                        "response": [response],
                        "inference_time": inference_time
                    }
                }

        except Exception as e:
            logger.error(f"本地模型处理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {
                "code": "ERROR",
                "msg": f"本地模型处理失败: {str(e)}",
                "data": ""
            }