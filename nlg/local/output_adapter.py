#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出格式适配器
确保本地模型输出格式与系统标准格式保持一致
"""

import json
import re
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class OutputAdapter:
    """
    输出格式适配器
    
    负责将本地模型的输出格式转换为系统标准格式
    """
    
    @staticmethod
    def remove_redundant_parentheses(text: str, input_text: str = "") -> str:
        """
        移除冗余的括号内容
        与nlg/ds/response_formatter.py保持一致
        """
        # 支持英文括号 () 和中文括号 （）
        pattern = r"\((.*?)\)|（(.*?)）"
        matches = [m[0] if m[0] else m[1] for m in re.findall(pattern, text)]

        for match in matches:
            if match in input_text or match in ["灯", "插座", "空调", "窗帘"]:
                # 构建可匹配英文或中文括号的模式
                escaped_match = re.escape(match)
                text = re.sub(rf"\(\s*{escaped_match}\s*\)|（\s*{escaped_match}\s*）", "", text)

        return text
    
    @staticmethod
    def parse_local_model_output(response: str) -> Optional[Dict[str, Any]]:
        """
        解析本地模型的JSON输出
        
        Args:
            response: 本地模型的原始响应
            
        Returns:
            Dict: 解析后的JSON对象，如果解析失败返回None
        """
        try:
            parsed_json = json.loads(response)
            return parsed_json
        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败: {e}, 原始响应: {response}")
            return None
    
    @staticmethod
    def extract_content_from_function_calls(parsed_json: Dict[str, Any]) -> str:
        """
        从function_calls格式中提取内容
        
        Args:
            parsed_json: 解析后的JSON对象
            
        Returns:
            str: 提取的内容
        """
        try:
            if "function_calls" in parsed_json and isinstance(parsed_json["function_calls"], list):
                function_calls = parsed_json["function_calls"]
                
                if not function_calls:
                    return "好的"
                
                # 处理多个函数调用
                contents = []
                for call in function_calls:
                    if isinstance(call, dict):
                        # 尝试从arguments.content中提取
                        if "arguments" in call and isinstance(call["arguments"], dict):
                            content = call["arguments"].get("content", "")
                            if content:
                                contents.append(str(content))
                        # 尝试从其他字段提取
                        elif "content" in call:
                            contents.append(str(call["content"]))
                        # 如果都没有，使用整个call对象的字符串表示
                        else:
                            contents.append(str(call))
                
                return " ".join(contents) if contents else "好的"
            
            return ""
            
        except Exception as e:
            logger.error(f"从function_calls提取内容失败: {e}")
            return ""
    
    @staticmethod
    def extract_content_from_single_call(parsed_json: Dict[str, Any]) -> str:
        """
        从单个函数调用格式中提取内容
        
        Args:
            parsed_json: 解析后的JSON对象
            
        Returns:
            str: 提取的内容
        """
        try:
            # 检查是否有arguments.content
            if "arguments" in parsed_json and isinstance(parsed_json["arguments"], dict):
                content = parsed_json["arguments"].get("content", "")
                if content:
                    return str(content)
            
            # 检查是否有直接的content字段
            if "content" in parsed_json:
                return str(parsed_json["content"])
            
            # 如果都没有，返回整个对象的字符串表示
            return str(parsed_json)
            
        except Exception as e:
            logger.error(f"从单个调用提取内容失败: {e}")
            return str(parsed_json)
    
    @classmethod
    def adapt_local_model_output(
        cls, 
        response: str, 
        query: str = "", 
        input_text: str = ""
    ) -> Dict[str, Any]:
        """
        适配本地模型输出为系统标准格式
        
        Args:
            response: 本地模型的原始响应
            query: 用户查询
            input_text: 用户输入文本
            
        Returns:
            Dict: 标准化的响应格式
        """
        try:
            # 如果响应以ERROR开头，直接返回错误格式
            if response.startswith("ERROR:"):
                return {
                    "code": "ERROR",
                    "msg": response,
                    "data": ""
                }
            
            # 尝试解析JSON
            parsed_json = cls.parse_local_model_output(response)
            
            if parsed_json is None:
                # JSON解析失败，直接使用原始响应
                cleaned_response = cls.remove_redundant_parentheses(response, input_text)
                return {
                    "code": "SUCCESS",
                    "msg": "成功",
                    "data": cleaned_response
                }
            
            # 提取内容
            content = ""
            
            # 处理标准的function_calls格式
            if "function_calls" in parsed_json:
                content = cls.extract_content_from_function_calls(parsed_json)
            
            # 处理单个函数调用格式
            elif "arguments" in parsed_json or "content" in parsed_json:
                content = cls.extract_content_from_single_call(parsed_json)
            
            # 处理其他格式
            else:
                content = str(parsed_json)
            
            # 如果没有提取到内容，使用默认响应
            if not content.strip():
                content = "好的"
            
            # 清理内容中的冗余括号
            cleaned_content = cls.remove_redundant_parentheses(content, input_text)
            
            return {
                "code": "SUCCESS",
                "msg": "成功",
                "data": cleaned_content
            }
            
        except Exception as e:
            logger.error(f"适配本地模型输出失败: {e}")
            return {
                "code": "ERROR",
                "msg": f"输出适配失败: {str(e)}",
                "data": ""
            }
    
    @classmethod
    def format_response_like_cloud(
        cls, 
        parsed_objs: Any, 
        input_text: str = ""
    ) -> Dict[str, Any]:
        """
        按照云端模型的格式化方式处理响应
        与nlg/ds/response_formatter.py的format_response保持一致
        
        Args:
            parsed_objs: 解析后的对象
            input_text: 输入文本
            
        Returns:
            Dict: 格式化后的响应
        """
        if not parsed_objs:
            return {
                "code": "SUCCESS",
                "msg": "成功",
                "data": "好的"
            }

        try:
            response_list = ""
            if isinstance(parsed_objs, list):
                contents = []
                for obj in parsed_objs:
                    if isinstance(obj, dict):
                        content = obj.get("content", "")
                        cleaned_content = cls.remove_redundant_parentheses(content, input_text)
                        contents.append(cleaned_content)
                response_list = " ".join(contents)
            elif isinstance(parsed_objs, dict):
                item_str = "；".join(f"{k}：{v}" for k, v in parsed_objs.items())
                response_list = cls.remove_redundant_parentheses(item_str, input_text)
            else:
                response_list = cls.remove_redundant_parentheses(str(parsed_objs), input_text)

            return {
                "code": "SUCCESS",
                "msg": "成功",
                "data": response_list if response_list.strip() else "好的"
            }
        except Exception as e:
            return {
                "code": "ERROR",
                "msg": f"识别失败: {str(e)}",
                "data": ""
            }

    def process_response(self, response: str, query: str = "") -> List[str]:
        """
        处理响应，返回标准格式的响应列表

        Args:
            response: 原始响应
            query: 用户查询

        Returns:
            List[str]: 处理后的响应列表
        """
        try:
            # 尝试解析JSON
            parsed_json = self.parse_local_model_output(response)

            if parsed_json is None:
                # 如果不是JSON，直接返回原始响应
                return [response]

            # 如果是单个函数调用格式，转换为标准格式
            if "name" in parsed_json and "arguments" in parsed_json:
                # 转换为标准格式
                return [response]  # 直接返回原始JSON字符串

            # 如果是function_calls格式
            elif "function_calls" in parsed_json:
                function_calls = parsed_json["function_calls"]
                if isinstance(function_calls, list) and function_calls:
                    # 返回第一个函数调用的JSON字符串
                    return [json.dumps(function_calls[0], ensure_ascii=False)]
                else:
                    return ["好的"]

            # 其他格式直接返回
            else:
                return [response]

        except Exception as e:
            logger.error(f"处理响应失败: {e}")
            return [response]
