TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "nlg",
            "description": "根据用户输入信息，通过自然语言处理润色优化，友好的输出",
            "parameters": {
                "type": "object",
                "properties": {
                    "content": {
                        "type": "string",
                        "description": (
                            "使用自然语言处理技术生成流畅、连贯且简洁的文本，以便向用户传达设备状态信息"
                            "1.设备状态报告优化规则（仅当输入包含设备状态数据时生效）"
                                "1. 如果输入中包含多个重复句式结构（如多次出现'主人，帮您查询到...'），请将其合并为一个简洁的汇总语句。"
                                "3. 输出不能有重复称呼（如“主人”）、括号内容，并保持口语化表达。"
                                "4. 不要添加额外解释或问候语，只需简洁明了地呈现结果"
                            "注意："
                            "1. 不要使用感叹号、问号或其他多余标点。"
                            "2. 保持中文口语化风格，不使用书面化或技术性术语。"
                            "3. 若某类设备所有状态都相同，可直接统一表述，例如'全部关闭'、'都在运行'等。"
                        ),
                    }
                },
                "required": ["content"],
            },
        },
    },
]