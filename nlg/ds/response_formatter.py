import re

def remove_redundant_parentheses(text, input_text):
    # 支持英文括号 () 和中文括号 （）
    pattern = r"$(.*?)$|（(.*?)）"
    matches = [m[0] if m[0] else m[1] for m in re.findall(pattern, text)]

    for match in matches:
        if match in input_text or match in ["灯", "插座", "空调", "窗帘"]:
            # 构建可匹配英文或中文括号的模式
            escaped_match = re.escape(match)
            text = re.sub(rf"$\s*{escaped_match}\s*$|（\s*{escaped_match}\s*）", "", text)

    return text
def format_response(parsed_objs, input_text=""):
    if not parsed_objs:
        return {
            "code": "SUCCESS",
            "msg": "成功",
            "data": {}
        }

    try:
        response_list = ""
        if isinstance(parsed_objs, list):
            contents = []
            for obj in parsed_objs:
                if isinstance(obj, dict):
                    content = obj.get("content", "")
                    cleaned_content = remove_redundant_parentheses(content, input_text)
                    contents.append(cleaned_content)
            response_list = " ".join(contents)
        elif isinstance(parsed_objs, dict):
            item_str = "；".join(f"{k}：{v}" for k, v in parsed_objs.items())
            response_list = remove_redundant_parentheses(item_str, input_text)
        else:
            response_list = remove_redundant_parentheses(str(parsed_objs), input_text)

        return {
            "code": "SUCCESS",
            "msg": "成功",
            "data": response_list
        }
    except Exception as e:
        return {
            "code": "ERROR",
            "msg": f"识别失败: {str(e)}",
            "data": {}
        }