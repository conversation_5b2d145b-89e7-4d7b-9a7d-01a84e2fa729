# mode/ds/llm_processor.py
import time
import json
from qwen_agent.llm import get_chat_model
from flask import current_app
from nlg.ds.function_extractor import extract_json_objects
from nlg.ds.response_formatter import format_response
from entity.config import Config
from nlg.ds import tools

class LLMProcessor:
    def __init__(self):
        self.config = Config
    def process_llm_call(self,messages,query: str):
        llm = get_chat_model({
            "model": self.config.model,
            "model_server": self.config.model_server,
            "api_key": self.config.api_key,
        })

        functions = [tool["function"] for tool in tools.TOOLS]

        start_time = time.time()
        responses = []
        for chunk in llm.chat(
            messages=messages,
            functions=functions,
            extra_generate_cfg=dict(parallel_function_calls=True, response_format={"type": "json_object"}),
        ):
            pass
        responses.extend(chunk)

        elapsed_time = time.time() - start_time
        current_app.logger.info(f"LLM 推理完成，耗时: {elapsed_time:.2f} 秒")
        current_app.logger.info("LLM 回复:"+json.dumps(responses, indent=2, ensure_ascii=False))
        result = ""
        for message in responses:
            content = message.get("reasoning_content", "")
            if not content:
                continue

            try:
                data = json.loads(content)
                #if isinstance(data, dict) and 'function_calls' in data:
                  #  results.extend([fc.get('arguments') for fc in data['function_calls']])
                #elif isinstance(data, list):
                   # results.extend([item.get('arguments') for item in data])
                #else:
                   # parsed_objs = extract_json_objects(content)
                    #results.extend([obj.get('arguments', obj.get('response',obj)) for obj in parsed_objs])
                if "tool_call" in data:
                        result = data.get("tool_call").get('arguments').get("content")
                elif "arguments" in data:
                        result = data.get("arguments").get("content")
                else:
                    result =data.get("content")
            except json.JSONDecodeError:
                # parsed_objs = extract_json_objects(content)
                # results.extend([obj.get('arguments') for obj in parsed_objs if 'arguments' in obj])
                result = content
    
            return {
                "code": "SUCCESS",
                "msg": "成功",
                "data": result
            }

        #return format_response(results,query)