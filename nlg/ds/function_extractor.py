
import json
import regex as re
from mistletoe import Document
from flask import current_app
import demjson3 as dem<PERSON><PERSON>
def extract_json_objects(text: str):
    """
    提取文本中的多个 JSON 函数调用对象。
    支持：
      - Markdown 代码块（```json ... ```）
      - <response> 标签包裹的内容
      - 纯文本中包含的多行 JSON
    返回值为 list of dict
    """

    # 尝试提取 <response> 中的内容
    response_match = re.search(r"<response>\s*(.*?)\s*</response>", text, re.DOTALL)
    if response_match:
        content_block = response_match.group(1).strip()
        lines = [line.strip() for line in content_block.split('\n')]
        parsed_list = []
        for line in lines:
            if not line:
                continue
            try:
                obj = json.loads(line)
                parsed_list.append(obj)
            except json.JSONDecodeError:
                pass
        if parsed_list:
            return parsed_list

    # 使用 mistletoe 提取 Markdown 代码块内容
    doc = Document(text)
    for ele in doc.children:
        if hasattr(ele, 'children') and len(ele.children) > 0:
            first_child = ele.children[0]
            if isinstance(first_child, str) and first_child.startswith('{'):
                lines = [line.strip() for line in first_child.strip().split('\n') if line.strip()]
                parsed_list = []
                for line in lines:
                    try:
                        obj = json.loads(line)
                        parsed_list.append(obj)
                    except json.JSONDecodeError as e:
                        current_app.logger.error(f"JSON 解析失败（Markdown 块中）: {e}")
                if parsed_list:
                    return parsed_list

    # 如果都没有，则尝试从纯文本中提取所有 { ... } 结构（使用 regex 支持递归）
    json_blocks = re.findall(r'\{(?:[^{}]|(?R))*\}', text, re.DOTALL)
    parsed_list = []

    # 新增逻辑：逐个清理并解析 JSON 块
    for block in json_blocks:
        # 清理换行、缩进等字符
        cleaned_block = block.replace('\n', '').replace(' ', '')
        try:
            obj = demjson.decode(cleaned_block)
            parsed_list.append(obj)
        except json.JSONDecodeError:
            # 可选：尝试更进一步的修复逻辑，比如补全引号缺失等
            current_app.logger.error(f"cleaned_block 解析失败: {e}")
            pass
     # 特殊情况处理：如果输入本身是一个合法 JSON 数组，且每个元素包含 "input" 字段
    try:
        full_obj = json.loads(text)
        if isinstance(full_obj, list):
            for item in full_obj:
                if isinstance(item, dict) and "content" in item:
                    content = item["content"].strip()
                    try:
                        inner_obj = json.loads(content)
                        if isinstance(inner_obj, dict) and "input" in inner_obj:
                            # 构造一个符合预期的 function_call 对象
                            return [{
                                "name": "nlg",
                                "arguments": {
                                    "content": inner_obj["input"]
                                }
                            }]
                    except json.JSONDecodeError:
                        continue
    except json.JSONDecodeError:
        pass

    return parsed_list