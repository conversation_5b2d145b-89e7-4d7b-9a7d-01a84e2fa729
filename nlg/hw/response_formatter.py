import re

# response_formatter.py

def format_response(parsed_objs, input_text=""):
    if not parsed_objs:
        return {"code": "SUCCESS", "msg": "成功", "data": ""}

    try:
        response_list = []
        for obj in parsed_objs:
            if isinstance(obj, dict) and "content" in obj:
                response_list.append(obj["content"])

        return {
            "code": "SUCCESS",
            "msg": "成功",
            "data": " ".join(response_list) if response_list else ""
        }
    except Exception as e:
        return {"code": "ERROR", "msg": f"识别失败: {str(e)}", "data": ""}