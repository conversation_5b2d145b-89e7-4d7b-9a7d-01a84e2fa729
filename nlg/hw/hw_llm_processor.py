import time,re
import json
from flask import current_app
from nlg.hw.function_extractor import extract_json_objects
from nlg.hw.response_formatter import format_response
from entity.config import Config
from openai import OpenAI

class HwLLMProcessor:
    def __init__(self):
        self.config = Config

    def process_llm_call(self,messages,query: str):
        client = OpenAI(
            api_key=self.config.api_key,
            base_url=self.config.model_server
        )

        start_time = time.time()
        
        # 发起非流式请求
        response = client.chat.completions.create(
            model=self.config.model,
            messages=messages,
            stream=False  # 明确关闭流式传输
        )

        # 正确解析响应
        responses = []
        if response.choices:
            for choice in response.choices:
                message = choice.message
                if message:  # 确保 message 存在
                    # 将 Message 对象转换为字典
                    responses.append(message.model_dump())

        elapsed_time = time.time() - start_time
        current_app.logger.info(f"HW LLM 推理完成，耗时: {elapsed_time:.2f} 秒")
        current_app.logger.info("HW LLM 回复:"+json.dumps(responses, indent=2, ensure_ascii=False))
        results = []
        for message in responses:
            content = message.get("content", "")
            if not content:
                continue

            try:
                # 新增：去除 Markdown 代码块包裹
                content = re.sub(r'^\s*```json\s*', '', content, flags=re.DOTALL).strip()
                content = re.sub(r'\s*```\s*$', '', content, flags=re.DOTALL).strip()

                data = json.loads(content)

                if isinstance(data, dict) and 'function_calls' in data:
                    results.extend([fc.get('arguments') for fc in data['function_calls']])
                elif isinstance(data, list):
                    results.extend([item.get('arguments') for item in data])
                else:
                    parsed_objs = extract_json_objects(content)
                    results.extend([obj.get('arguments', obj.get('response', obj)) for obj in parsed_objs])

            except json.JSONDecodeError:
                current_app.logger.warning("JSON 解析失败，尝试通过 extract_json_objects 提取")
                parsed_objs = extract_json_objects(content)
                results.extend([obj.get('arguments') for obj in parsed_objs if 'arguments' in obj])


        return format_response(results, query)