import time,re
import json
from flask import current_app
from nlg.qwen3.function_extractor import extract_json_objects
from nlg.qwen3.response_formatter import format_response
from entity.config import Config
from http import HTTPStatus
import dashscope

class QwLLMProcessor:
    def __init__(self):
        self.config = Config

    def process_llm_call(self,messages,query: str):
        start_time = time.time()
        
        response = dashscope.Generation.call(
            model=self.config.model,
            api_key=self.config.api_key,
            messages=messages,
            stream=False,
            result_format='message',  # 将返回结果格式设置为 message
            top_p=0.8,
            temperature=0.7,
            enable_search=False,
            enable_thinking=False,
            thinking_budget=1
        )
        choices =[]
        if response.status_code == HTTPStatus.OK:
             choices = response.output.choices
        else:
            current_app.logger.error('Request id: %s, Status code: %s, error code: %s, error message: %s' % (
                response.request_id, response.status_code,
                response.code, response.message
            ))
        elapsed_time = time.time() - start_time
        current_app.logger.info(f"QW NLG 推理完成,耗时: {elapsed_time:.2f} 秒")
        current_app.logger.info("QW LLM 回复:" + json.dumps(response, indent=2, ensure_ascii=False))
        results = []
        for choice in choices:
            message = choice["message"]
            content = message.get("content", "")
            if content:
              results.append(content)
        return format_response(results, query)