# mode/ds/function_extractor.py
import json
import regex as re
from flask import current_app
import demjson3 as demjson
# function_extractor.py

def extract_json_objects(text: str):
    """
    提取文本中的多个 JSON 函数调用对象。
    支持：
      - Markdown 代码块（```json ... ```）
      - <response> 标签包裹的内容
      - 纯文本中包含的多行 JSON
    返回值为 list of dict
    """

    # 尝试提取 Markdown 代码块中的 JSON
    markdown_match = re.search(r"```json\s*(.*?)\s*```", text, re.DOTALL)
    if markdown_match:
        content_block = markdown_match.group(1).strip()
        try:
            obj = json.loads(content_block)
            if isinstance(obj, list):
                return obj
            elif isinstance(obj, dict):
                return [obj]
        except json.JSONDecodeError:
            pass

    # 如果没有找到 Markdown 块，则尝试从纯文本中提取 { ... } 结构
    json_blocks = re.findall(r'\{(?:[^{}]|(?R))*\}', text, re.DOTALL)
    parsed_list = []

    for block in json_blocks:
        cleaned_block = block.strip()
        try:
            obj = json.loads(cleaned_block)
            parsed_list.append(obj)
        except json.JSONDecodeError:
            try:
                # 可选：尝试更宽松的解析器 demjson
                obj = demjson.decode(cleaned_block)
                parsed_list.append(obj)
            except Exception as e:
                current_app.logger.error(f"JSON 解析失败（raw block）: {e}")
                pass

    return parsed_list