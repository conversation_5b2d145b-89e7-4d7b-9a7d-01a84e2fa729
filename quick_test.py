#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 用于快速验证API功能
"""

import requests
import json
import time
from datetime import datetime
import random

def get_enhanced_payload(query):
    """获取增强的请求负载，包含丰富的设备和房间信息"""
    return {
        "query": query,
        "traceId": f"test_{int(time.time())}_{random.randint(1000, 9999)}",
        "context": {
            "scenes": [
                "测试专用",
                "会客模式", 
                "离家模式",
                "睡眠模式",
                "用餐模式",
                "回家模式",
                "晨起模式",
                "全开模式",
                "全关模式"
            ],
            "rooms": [
                "客厅",
                "主卧", 
                "客卧",
                "次卧",
                "书房",
                "卫生间",
                "餐厅",
                "厨房",
                "阳台",
                "走廊",
                "玄关",
                "儿童房",
                "过道",
                "全屋",
                "海上"
            ],
            "devices": [
                # 主卧灯具
                {"name": "筒灯一", "domain": "灯", "space_name": "主卧"},
                {"name": "筒灯", "domain": "灯", "space_name": "主卧"},
                {"name": "明装筒灯", "domain": "灯", "space_name": "主卧"},
                {"name": "明装筒灯一", "domain": "灯", "space_name": "主卧"},
                {"name": "吸顶灯一", "domain": "灯", "space_name": "主卧"},
                {"name": "氛围灯", "domain": "灯", "space_name": "主卧"},
                
                # 全屋灯具
                {"name": "全屋明装筒灯", "domain": "灯", "space_name": "全屋"},
                {"name": "全屋灯", "domain": "灯", "space_name": "全屋"},
                
                # 通断器设备
                {"name": "一路通断器", "domain": "开关", "space_name": "主卧"},
                {"name": "通断器", "domain": "开关", "space_name": "主卧"},
                {"name": "通断器一", "domain": "开关", "space_name": "主卧"},
                {"name": "6路通断器灯一", "domain": "开关", "space_name": "主卧"},
                {"name": "全屋通断器", "domain": "开关", "space_name": "全屋"},
                
                # 过道设备
                {"name": "过道三开", "domain": "开关", "space_name": "过道"},
                
                # 特殊测试设备
                {"name": "海上的灯", "domain": "灯", "space_name": "海上"},
                
                # 窗帘设备
                {"name": "纱帘", "domain": "窗帘", "space_name": "主卧"},
                {"name": "布帘", "domain": "窗帘", "space_name": "主卧"},
                {"name": "窗帘电机", "domain": "窗帘", "space_name": "主卧"},
                {"name": "窗帘", "domain": "窗帘", "space_name": "主卧"},
                {"name": "全屋窗帘", "domain": "窗帘", "space_name": "全屋"},
                {"name": "窗帘面板一", "domain": "窗帘", "space_name": "主卧"},
                {"name": "窗帘面板", "domain": "窗帘", "space_name": "主卧"},
                {"name": "窗帘一", "domain": "窗帘", "space_name": "主卧"},
                {"name": "全屋窗帘面板", "domain": "窗帘", "space_name": "全屋"},
                
                # 空调设备
                {"name": "空调一", "domain": "空调", "space_name": "主卧"},
                {"name": "空调", "domain": "空调", "space_name": "主卧"},
                {"name": "主卧空调", "domain": "空调", "space_name": "主卧"},
                {"name": "客厅东空调", "domain": "空调", "space_name": "客厅"},
                {"name": "客厅西空调", "domain": "空调", "space_name": "客厅"},
                {"name": "客厅空调", "domain": "空调", "space_name": "客厅"},
                {"name": "全家空调", "domain": "空调", "space_name": "全屋"},
                
                # 开关设备
                {"name": "开关一", "domain": "开关", "space_name": "次卧"},
                {"name": "开关", "domain": "开关", "space_name": "次卧"},
                {"name": "餐厅筒灯", "domain": "灯", "space_name": "客厅"},
                {"name": "筒灯", "domain": "灯", "space_name": "客厅"},
                {"name": "客厅灯", "domain": "灯", "space_name": "客厅"},
                {"name": "全家开关", "domain": "开关", "space_name": "全屋"},
                {"name": "主卧双开", "domain": "开关", "space_name": "主卧"},
                {"name": "开关三", "domain": "开关", "space_name": "书房"},
                
                # 插座设备
                {"name": "插座一", "domain": "插座", "space_name": "主卧"},
                {"name": "插座", "domain": "插座", "space_name": "主卧"},
                {"name": "全家插座", "domain": "插座", "space_name": "全屋"},
                
                # 地暖设备
                {"name": "地暖一", "domain": "地暖", "space_name": "主卧"},
                {"name": "地暖", "domain": "地暖", "space_name": "主卧"},
                {"name": "全家地暖", "domain": "地暖", "space_name": "全屋"},
                
                # 新风设备
                {"name": "新风", "domain": "新风机", "space_name": "主卧"},
                {"name": "新风一", "domain": "新风机", "space_name": "主卧"},
                {"name": "新风机", "domain": "新风机", "space_name": "主卧"},
                {"name": "全家新风机", "domain": "新风机", "space_name": "全屋"},
                
                # 厨房设备
                {"name": "油烟机", "domain": "油烟机", "space_name": "厨房"},
                {"name": "电饭煲", "domain": "电饭煲", "space_name": "厨房"},
                {"name": "微波炉", "domain": "微波炉", "space_name": "厨房"},
                
                # 其他设备
                {"name": "洗衣机", "domain": "洗衣机", "space_name": "阳台"},
                {"name": "按摩椅", "domain": "按摩椅", "space_name": "客厅"},
                {"name": "加湿器", "domain": "加湿器", "space_name": "客厅"},
                {"name": "投影", "domain": "投影仪", "space_name": "客厅"},
            ],
            "candidate": {
                "domains": [],
                "devices": []
            },
            "homeGraph": {
                "name": "我的家庭",
                "id": "1283",
                "position": {
                    "adName": "",
                    "cityName": "",
                    "pName": "",
                    "cityCode": "440600",
                    "latitude": "22.928231957573892",
                    "adCode": "440606102",
                    "pCode": "440000",
                    "name": "广东省佛山市顺德区北滘镇",
                    "longitude": "113.20699596198394",
                    "typeCode": ""
                }
            }
        }
    }

def quick_test(query, base_url="http://127.0.0.1:8578"):
    """快速测试单个查询"""
    url = base_url + "/send_msg"
    payload = get_enhanced_payload(query)
    headers = {"Content-Type": "application/json"}
    
    print("=" * 80)
    print(f"🧪 快速测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    print(f"📝 测试查询: {query}")
    print(f"📡 请求URL: {url}")
    print("-" * 80)
    
    try:
        start_time = time.time()
        print("🚀 发送请求中...")
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        end_time = time.time()
        request_time = end_time - start_time
        
        print(f"⏱️  请求耗时: {request_time:.2f}秒")
        print(f"📊 响应状态码: {response.status_code}")
        print("-" * 80)
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print("✅ 请求成功!")
                print("📄 响应内容:")
                print(json.dumps(response_data, ensure_ascii=False, indent=2))
                
                if response_data.get("code") == "SUCCESS":
                    print("\n🎉 API调用成功!")
                    data = response_data.get("data", {})
                    if "inference_time" in data:
                        print(f"🤖 模型推理时间: {data['inference_time']:.2f}秒")
                    if "response" in data:
                        print(f"💬 模型响应: {data['response']}")
                else:
                    print(f"\n❌ API返回错误: {response_data.get('msg', '未知错误')}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
                
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时 (30秒)")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请检查服务是否启动")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

    print("=" * 80)
    print("🏁 测试完成")
    print("=" * 80)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        query = " ".join(sys.argv[1:])
        quick_test(query)
    else:
        # 默认测试查询
        default_query = "打开主卧的筒灯一"
        print("💡 使用方法: python quick_test.py \"你的测试查询\"")
        print(f"🔧 使用默认查询进行测试: {default_query}")
        print()
        quick_test(default_query)
