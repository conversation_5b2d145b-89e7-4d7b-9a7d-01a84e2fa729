
import os
import json
from entity.config import Config
from dotenv import load_dotenv

class ConfigLoader:
    def __init__(self, env_path='.env'):
        # 获取项目根目录（app.py所在目录）
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.env_path = os.path.join(project_root, env_path)

    def load(self):
        if os.path.exists(self.env_path):
            load_dotenv(self.env_path, override=True)  # 强制重新加载
        else:
            print(f".env 文件不存在于路径: {self.env_path}")
        
        try:
            Config.mode_type = os.getenv('MODE_TYPE', '')
            Config.WEATHER_INDEX = json.loads(os.getenv('WEATHER_INDEX', '[]'))
            Config.LIVING = json.loads(os.getenv('LIVING', '[]'))
            Config.DEVICE_LIST = json.loads(os.getenv('DEVICE_LIST', '[]'))
            if  Config.mode_type == 'hw':
                Config.model = os.getenv('HW_MODE', '')
                Config.model_server = os.getenv('HW_MODE_SERVER', '')
                Config.api_key = os.getenv('HW_API_KEY', '')
            elif Config.mode_type == 'qw':
                Config.model = os.getenv('QW_MODE', '')
                Config.model_server = os.getenv('QW_MODE_SERVER', '')
                Config.api_key = os.getenv('QW_API_KEY', '')
            else:
                Config.model = os.getenv('MODE', '')
                Config.model_server = os.getenv('MODE_SERVER', '')
                Config.api_key = os.getenv('API_KEY', '')
    
        except Exception as e:
            print("解析 .env 配置失败:", e)