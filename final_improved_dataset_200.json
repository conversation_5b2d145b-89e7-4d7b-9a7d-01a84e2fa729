[{"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "自我介绍一下", "output": "{\"function_calls\": [{\"name\": \"xiaoling\", \"arguments\": {\"intent\": \"自我介绍\", \"domain\": \"对话\", \"content\": \"我是小灵，您的智能家居助手，可以帮您控制家中的各种设备。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "能关闭卧室插座吗", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭插座\", \"domain\": \"插座\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "卧室空调温度调到最高", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高客厅空调温度，接下来打开卧室插座", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\", \"value\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "请关闭客厅地暖", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭地暖\", \"domain\": \"地暖\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭客厅插座，同时启动睡眠模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭插座\", \"domain\": \"插座\", \"room\": \"客厅\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "你叫什么名字", "output": "{\"function_calls\": [{\"name\": \"xiaoling\", \"arguments\": {\"intent\": \"自我介绍\", \"domain\": \"对话\", \"content\": \"我是小灵，您的智能家居助手，可以帮您控制家中的各种设备。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询一下天气", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "请关闭阳台灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"阳台\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开书房的窗帘", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"书房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我打开客厅主灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"客厅主灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "客厅空调温度调到最高", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭卧室吸顶灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调低阳台灯光亮度", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"room\": \"阳台\", \"device\": \"\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "启动阅读模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"阅读模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭客厅插座，接着启动阅读模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭插座\", \"domain\": \"插座\", \"room\": \"客厅\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"阅读模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "后天天气预报", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+2\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭卧室窗帘，最后启动观影模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"观影模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开客厅地暖，打开卧室空调，接着启动离家模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开地暖\", \"domain\": \"地暖\", \"room\": \"客厅\", \"device\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"离家模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询天气，然后打开书房窗帘", "output": "{\"function_calls\": [{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"书房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "设置客厅主灯亮度48%", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置亮度\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"客厅主灯\", \"value\": \"48\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开一下阳台灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"阳台\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "麻烦打开一下卧室的灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询空气质量，接着关闭新风系统", "output": "{\"function_calls\": [{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭新风\", \"domain\": \"新风\", \"room\": \"客厅\", \"device\": \"新风系统\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "重启", "output": "{\"function_calls\": [{\"name\": \"dialog\", \"arguments\": {\"intent\": \"重新开始\", \"domain\": \"对话\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "麻烦关闭一下卧室的插座", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭插座\", \"domain\": \"插座\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "开启睡眠模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "还好吗", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"我运行正常！请问需要我帮您做什么？\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "我想打开阳台灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"阳台\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "执行回家模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"回家模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭主卧地暖，最后启动睡眠模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "后天天气预报", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+2\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高客厅空调温度，然后打开卧室插座", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\", \"value\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "太好了", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"您太客气了！为您服务是我的荣幸。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "请打开厨房灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"厨房\", \"device\": \"厨房射灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "晚上好", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"您好！很高兴为您服务，请问需要控制哪些设备呢？\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "有多少个设备", "output": "{\"function_calls\": [{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"room\": \"默认\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开阳台灯，接着启动阅读模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"阳台\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"阅读模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "有多少个设备", "output": "{\"function_calls\": [{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"room\": \"默认\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "真不错", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"谢谢！能让您满意是我最大的成就。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭主卧地暖，还有关闭客厅窗帘", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开主卧地暖，和关闭卧室空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "有多少个设备", "output": "{\"function_calls\": [{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"room\": \"默认\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我打开书房台灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "我想查询运动指数", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询运动指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "设备状态查询", "output": "{\"function_calls\": [{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"room\": \"默认\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把厨房射灯亮度调高", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"room\": \"厨房\", \"device\": \"厨房射灯\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开主卧地暖，然后启动回家模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"回家模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我查询运动指数", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询运动指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高卧室空调温度", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开一下客厅插座", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "麻烦关闭一下书房的空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "后天天气预报", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+2\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把客厅主灯亮度调高", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"客厅主灯\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询一下天气", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "重置", "output": "{\"function_calls\": [{\"name\": \"dialog\", \"arguments\": {\"intent\": \"重新开始\", \"domain\": \"对话\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开新风系统，同时启动聚会模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开新风\", \"domain\": \"新风\", \"room\": \"客厅\", \"device\": \"新风系统\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"聚会模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭客厅插座，打开客厅主灯，然后启动聚会模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭插座\", \"domain\": \"插座\", \"room\": \"客厅\", \"device\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"客厅主灯\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"聚会模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "退出", "output": "{\"function_calls\": [{\"name\": \"dialog\", \"arguments\": {\"intent\": \"终止对话\", \"domain\": \"对话\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "启动休息模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"休息模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我关闭卧室吸顶灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "知道了", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"好的！还有什么需要我帮您处理的吗？\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高卧室空调温度，再关闭书房空调", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"\", \"value\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询空气质量，接着关闭客厅主灯", "output": "{\"function_calls\": [{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"客厅主灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "今天天气怎么样", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭厨房插座，接着启动回家模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭插座\", \"domain\": \"插座\", \"room\": \"厨房\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"回家模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询感冒指数情况", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询感冒指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开客厅主灯，接着启动回家模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"客厅主灯\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"回家模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "多谢", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"不用谢！如果还需要控制其他设备，随时告诉我。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭书房的灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "我想查询穿衣指数", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询穿衣指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开客厅的窗帘", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把卧室吸顶灯亮度调高", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "谢谢", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"不客气！很高兴能帮到您，还有其他需要吗？\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "早上好", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"您好！很高兴为您服务，请问需要控制哪些设备呢？\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "后天天气预报", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+2\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "执行睡眠模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "执行休息模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"休息模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开书房台灯，打开厨房插座，最后启动阅读模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"厨房\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"阅读模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "自我介绍一下", "output": "{\"function_calls\": [{\"name\": \"xiaoling\", \"arguments\": {\"intent\": \"自我介绍\", \"domain\": \"对话\", \"content\": \"我是小灵，您的智能家居助手，可以帮您控制家中的各种设备。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "介绍你自己", "output": "{\"function_calls\": [{\"name\": \"xiaoling\", \"arguments\": {\"intent\": \"自我介绍\", \"domain\": \"对话\", \"content\": \"我是小灵，您的智能家居助手，可以帮您控制家中的各种设备。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "明天会下雨吗", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+1\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开书房台灯，还有打开客厅窗帘", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "goodbye", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"拜拜！期待下次为您服务。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询化妆指数情况", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询化妆指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "我想打开卧室灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "今天天气怎么样", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询一下天气", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "书房空调温度调到最高", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开书房空调，还有打开客厅空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭一下客厅空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "能打开客厅插座吗", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查看设备情况", "output": "{\"function_calls\": [{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"room\": \"默认\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把主卧地暖调高一点", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高客厅地暖，然后打开主卧地暖", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高地暖\", \"domain\": \"地暖\", \"room\": \"客厅\", \"device\": \"\", \"value\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "今天紫外线指数", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询紫外线指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "执行工作模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"工作模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把书房台灯亮度调低", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把主卧地暖设置为18度", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置地暖温度\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\", \"value\": \"18\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我关闭书房台灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高厨房射灯亮度，接下来打开卧室空调", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"room\": \"厨房\", \"device\": \"厨房射灯\", \"value\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "我想打开厨房灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"厨房\", \"device\": \"厨房射灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开阳台灯，最后启动睡眠模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"阳台\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "切换到清洁模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"清洁模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "请打开客厅新风", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开新风\", \"domain\": \"新风\", \"room\": \"客厅\", \"device\": \"新风系统\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我查询感冒指数", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询感冒指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "将客厅空调温度调到29", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\", \"value\": \"29\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭一下客厅窗帘", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "您好", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"您好！我可以帮您控制家中的各种智能设备，请告诉我您的需求。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "退出", "output": "{\"function_calls\": [{\"name\": \"dialog\", \"arguments\": {\"intent\": \"终止对话\", \"domain\": \"对话\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "我想打开客厅新风", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开新风\", \"domain\": \"新风\", \"room\": \"客厅\", \"device\": \"新风系统\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我打开卧室窗帘", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "知道了", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"好的！我会继续为您提供服务。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭卧室的插座", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭插座\", \"domain\": \"插座\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调低书房空调温度", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低温度\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高客厅地暖，同时关闭客厅空调", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高地暖\", \"domain\": \"地暖\", \"room\": \"客厅\", \"device\": \"\", \"value\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "切换到用餐模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"用餐模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭卧室吸顶灯，接着启动观影模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"观影模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "请关闭卧室灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "能关闭卧室空调吗", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把主卧地暖调低一点", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "有多少个设备", "output": "{\"function_calls\": [{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"room\": \"默认\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "执行阅读模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"阅读模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "今天天气怎么样", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "敏感话题", "output": "{\"function_calls\": [{\"name\": \"dialog\", \"arguments\": {\"intent\": \"敏感词\", \"domain\": \"对话\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "介绍你自己", "output": "{\"function_calls\": [{\"name\": \"xiaoling\", \"arguments\": {\"intent\": \"自我介绍\", \"domain\": \"对话\", \"content\": \"我是小灵，您的智能家居助手，可以帮您控制家中的各种设备。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询空气质量，然后打开客厅插座", "output": "{\"function_calls\": [{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "请关闭厨房插座", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭插座\", \"domain\": \"插座\", \"room\": \"厨房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我关闭卧室空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "今天天气怎么样", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭客厅的窗帘", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "切换到清洁模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"清洁模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开客厅地暖，还有关闭客厅空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开地暖\", \"domain\": \"地暖\", \"room\": \"客厅\", \"device\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭书房台灯，最后启动观影模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"观影模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把卧室吸顶灯亮度设为13%", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置亮度\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\", \"value\": \"13\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "我想打开卧室插座", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭客厅的空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭客厅窗帘", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭窗帘\", \"domain\": \"窗帘\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "设置主卧地暖温度26度", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置地暖温度\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\", \"value\": \"26\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询设备数量", "output": "{\"function_calls\": [{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"room\": \"默认\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高书房空调温度", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "开启休息模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"休息模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭客厅插座，接着启动聚会模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭插座\", \"domain\": \"插座\", \"room\": \"客厅\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"聚会模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "麻烦打开一下客厅的空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "嗨", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"您好！我是小灵，您的智能家居助手，有什么可以帮您的吗？\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "麻烦打开一下厨房的插座", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"厨房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "切换到回家模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"回家模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我查询化妆指数", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询化妆指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭客厅的地暖", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭地暖\", \"domain\": \"地暖\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高厨房射灯亮度，再关闭书房台灯", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"room\": \"厨房\", \"device\": \"厨房射灯\", \"value\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "能打开厨房插座吗", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"厨房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "明天会下雨吗", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+1\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "启动清洁模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"清洁模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "很好", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"很高兴得到您的认可！有其他需要请随时告诉我。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调低客厅灯光亮度", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"客厅主灯\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "设备状态查询", "output": "{\"function_calls\": [{\"name\": \"queryDevice\", \"arguments\": {\"intent\": \"设备数量查询\", \"domain\": \"\", \"room\": \"默认\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把书房空调调到最小", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低温度\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高卧室地暖", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询穿衣指数情况", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询穿衣指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把阳台灯亮度调高", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"room\": \"阳台\", \"device\": \"\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高客厅地暖，接下来关闭客厅主灯", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高地暖\", \"domain\": \"地暖\", \"room\": \"客厅\", \"device\": \"\", \"value\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"客厅主灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "今天天气怎么样", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭主卧地暖，然后启动回家模式", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"回家模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "后天天气预报", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+2\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调节厨房灯光亮度到57%", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置亮度\", \"domain\": \"灯\", \"room\": \"厨房\", \"device\": \"厨房射灯\", \"value\": \"57\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高书房空调温度，再打开厨房射灯", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高温度\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\", \"value\": \"\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开灯\", \"domain\": \"灯\", \"room\": \"厨房\", \"device\": \"厨房射灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开客厅插座", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭一下阳台灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"阳台\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "你叫什么名字", "output": "{\"function_calls\": [{\"name\": \"xiaoling\", \"arguments\": {\"intent\": \"自我介绍\", \"domain\": \"对话\", \"content\": \"我是小灵，您的智能家居助手，可以帮您控制家中的各种设备。\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询温度，接着打开厨房插座", "output": "{\"function_calls\": [{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}, {\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开插座\", \"domain\": \"插座\", \"room\": \"厨房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "能打开书房空调吗", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把书房空调设置为23", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\", \"value\": \"23\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "切换到回家模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"回家模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "开启清洁模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"清洁模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "打开卧室的窗帘", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开窗帘\", \"domain\": \"窗帘\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭一下厨房射灯", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"厨房\", \"device\": \"厨房射灯\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我关闭客厅地暖", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭地暖\", \"domain\": \"地暖\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "能打开主卧地暖吗", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开地暖\", \"domain\": \"地暖\", \"room\": \"卧室\", \"device\": \"主卧地暖\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "重置", "output": "{\"function_calls\": [{\"name\": \"dialog\", \"arguments\": {\"intent\": \"重新开始\", \"domain\": \"对话\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "启动阅读模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"阅读模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "怎么样", "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"我运行正常！请问需要我帮您做什么？\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "查询运动指数情况", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询运动指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "我想打开客厅空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "执行观影模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"观影模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "关闭书房空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "帮我查询钓鱼指数", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询钓鱼指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "今天紫外线指数", "output": "{\"function_calls\": [{\"name\": \"getLiving\", \"arguments\": {\"intent\": \"查询紫外线指数\", \"domain\": \"生活指数\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调高书房灯光亮度", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "能打开卧室空调吗", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"打开空调\", \"domain\": \"空调\", \"room\": \"卧室\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "我想关闭客厅空调", "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "启动阅读模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"阅读模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调低卧室灯光亮度", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调低亮度\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "结束对话", "output": "{\"function_calls\": [{\"name\": \"dialog\", \"arguments\": {\"intent\": \"终止对话\", \"domain\": \"对话\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "退出", "output": "{\"function_calls\": [{\"name\": \"dialog\", \"arguments\": {\"intent\": \"终止对话\", \"domain\": \"对话\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "执行回家模式", "output": "{\"function_calls\": [{\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"回家模式\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把书房台灯亮度调高", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"调高亮度\", \"domain\": \"灯\", \"room\": \"书房\", \"device\": \"书房台灯\", \"value\": \"\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把客厅空调设置为16", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置温度\", \"domain\": \"空调\", \"room\": \"客厅\", \"device\": \"\", \"value\": \"16\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "后天天气预报", "output": "{\"function_calls\": [{\"name\": \"getWeat<PERSON>\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+2\", \"unit\": \"day\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "把客厅主灯亮度设为16%", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置亮度\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"客厅主灯\", \"value\": \"16\"}}]}"}, {"instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释", "input": "调节厨房灯光亮度到94%", "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置亮度\", \"domain\": \"灯\", \"room\": \"厨房\", \"device\": \"厨房射灯\", \"value\": \"94\"}}]}"}]