# 小灵智能家居控制系统 - 文档中心

欢迎来到小灵智能家居控制系统的文档中心。这里包含了系统的完整文档，帮助您快速上手、深入了解和有效使用本系统。

## 📚 文档导航

### 🚀 快速开始
- **[快速开始指南](QUICKSTART.md)** - 5分钟快速部署和测试系统

### 📋 用户文档
- **[API接口文档](API.md)** - 详细的API接口说明和使用示例
- **[配置指南](CONFIG.md)** - 环境变量、设备配置和系统配置说明

### 👨‍💻 开发文档  
- **[开发者指南](DEVELOPER.md)** - 系统架构、扩展指南和开发规范

### 🚀 运维文档
- **[部署运维文档](DEPLOYMENT.md)** - 部署步骤、监控、日志和故障排查

## 📖 文档概览

### 快速开始指南
适合初次使用的用户，包含：
- 环境准备和依赖安装
- 基础配置和服务启动
- 功能测试和验证
- 常见问题解决

### API接口文档
详细的API使用说明，包含：
- 接口列表和参数说明
- 请求响应格式
- 错误处理和状态码
- 使用示例和最佳实践

### 配置指南
全面的配置说明，包含：
- 环境变量配置
- AI模型配置
- 设备和场景配置
- 安全和性能配置

### 开发者指南
深入的技术文档，包含：
- 系统架构和设计
- 代码结构和模块说明
- 扩展开发指南
- 代码规范和最佳实践

### 部署运维文档
生产环境指南，包含：
- 多种部署方式
- 监控和健康检查
- 日志管理和分析
- 故障排查和恢复

## 🎯 按角色查看文档

### 👤 最终用户
如果您是系统的最终用户，建议阅读：
1. [快速开始指南](QUICKSTART.md) - 了解基本使用
2. [API接口文档](API.md) - 学习接口调用

### 🔧 系统管理员
如果您负责系统部署和维护，建议阅读：
1. [配置指南](CONFIG.md) - 了解系统配置
2. [部署运维文档](DEPLOYMENT.md) - 学习部署和运维
3. [快速开始指南](QUICKSTART.md) - 快速验证部署

### 👨‍💻 开发者
如果您要进行二次开发或扩展，建议阅读：
1. [开发者指南](DEVELOPER.md) - 了解系统架构
2. [API接口文档](API.md) - 学习接口设计
3. [配置指南](CONFIG.md) - 了解配置机制

## 🔍 按场景查看文档

### 🆕 初次部署
1. [快速开始指南](QUICKSTART.md) - 快速部署测试
2. [配置指南](CONFIG.md) - 详细配置说明
3. [部署运维文档](DEPLOYMENT.md) - 生产环境部署

### 🔌 接口集成
1. [API接口文档](API.md) - 接口详细说明
2. [快速开始指南](QUICKSTART.md) - 快速测试验证

### 🛠️ 功能扩展
1. [开发者指南](DEVELOPER.md) - 架构和扩展说明
2. [API接口文档](API.md) - 接口设计参考

### 🚨 问题排查
1. [部署运维文档](DEPLOYMENT.md) - 故障排查指南
2. [配置指南](CONFIG.md) - 配置问题解决
3. [快速开始指南](QUICKSTART.md) - 基础问题排查

## 📋 文档更新记录

| 文档 | 最后更新 | 版本 | 主要变更 |
|------|----------|------|----------|
| 快速开始指南 | 2023-12-01 | v1.0 | 初始版本 |
| API接口文档 | 2023-12-01 | v1.0 | 初始版本 |
| 配置指南 | 2023-12-01 | v1.0 | 初始版本 |
| 开发者指南 | 2023-12-01 | v1.0 | 初始版本 |
| 部署运维文档 | 2023-12-01 | v1.0 | 初始版本 |

## 🤝 文档贡献

我们欢迎您为文档做出贡献！

### 如何贡献
1. **发现问题**: 在使用过程中发现文档问题或不清楚的地方
2. **提交Issue**: 在GitHub上提交文档相关的Issue
3. **提交PR**: 直接修改文档并提交Pull Request
4. **反馈建议**: 通过邮件或其他方式提供改进建议

### 文档规范
- 使用Markdown格式
- 保持结构清晰，层次分明
- 提供具体的示例和代码
- 及时更新过时的信息

### 文档维护
- 定期检查文档的准确性
- 根据系统更新及时更新文档
- 收集用户反馈并改进文档质量

## 📞 获取帮助

如果您在使用文档过程中遇到问题：

### 📖 查看文档
- 首先查看相关文档章节
- 使用文档内的搜索功能
- 查看常见问题部分

### 💬 社区支持
- 提交GitHub Issue
- 参与社区讨论
- 查看已有的Issue和解答

### 📧 联系我们
- 邮件: [<EMAIL>]
- 项目主页: [project-homepage]
- 技术支持: [support-contact]

## 🔗 相关链接

- **项目主页**: [GitHub Repository]
- **在线文档**: [Documentation Site]
- **API测试**: [API Testing Tool]
- **问题反馈**: [Issue Tracker]
- **更新日志**: [CHANGELOG.md](../CHANGELOG.md)
- **许可证**: [LICENSE](../LICENSE)

---

**提示**: 建议将本文档加入书签，以便快速访问各种文档资源。如果您是首次使用，强烈建议从[快速开始指南](QUICKSTART.md)开始。
