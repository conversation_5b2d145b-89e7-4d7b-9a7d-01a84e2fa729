# API接口文档

本文档详细描述了小灵智能家居控制系统的所有API接口。

## 基础信息

- **Base URL**: `http://localhost:8578`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

所有API接口都遵循统一的响应格式：

```json
{
  "code": "SUCCESS|ERROR",
  "msg": "响应消息",
  "data": "响应数据"
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| SUCCESS | 请求成功 |
| ERROR | 请求失败 |

## API接口列表

### 1. 健康检查

检查服务是否正常运行。

**接口地址**: `GET /`

**请求参数**: 无

**响应示例**:
```
hello world
```

---

### 2. 发送控制指令

处理智能家居控制指令，支持设备控制和场景控制。

**接口地址**: `POST /send_msg`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| query | string | 是 | 用户输入的控制指令 |
| traceId | string | 是 | 请求追踪ID |
| context | object | 是 | 上下文信息 |

**context对象结构**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scenes | array | 否 | 可用场景列表 |
| rooms | array | 否 | 房间列表 |
| devices | array | 是 | 设备列表 |
| homeGraph | object | 否 | 家庭图谱信息 |

**devices数组元素结构**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 设备名称 |
| spaceName | string | 是 | 所在空间名称 |
| intentDomainName | string | 是 | 设备类型域名 |

**homeGraph对象结构**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| position | object | 否 | 位置信息 |

**请求示例**:

```json
{
  "query": "打开客厅的灯并将亮度调到80%",
  "traceId": "req-20231201-001",
  "context": {
    "scenes": ["回家模式", "睡眠模式"],
    "rooms": ["客厅", "卧室", "厨房"],
    "devices": [
      {
        "name": "客厅主灯",
        "spaceName": "客厅", 
        "intentDomainName": "灯"
      },
      {
        "name": "客厅空调",
        "spaceName": "客厅",
        "intentDomainName": "空调"
      }
    ],
    "homeGraph": {
      "position": {
        "name": "北京市朝阳区"
      }
    }
  }
}
```

**响应示例**:

**成功响应**:
```json
{
  "code": "SUCCESS",
  "msg": "成功",
  "data": {
    "response": [
      "intent:turn_on;domain:灯;room:客厅;device:客厅主灯",
      "intent:adjust;domain:灯;room:客厅;device:客厅主灯;value:80"
    ]
  }
}
```

**设备未找到响应**:
```json
{
  "code": "SUCCESS", 
  "msg": "成功",
  "data": {
    "response": ["DEVICE_NOT_FOUND&设备名称"]
  }
}
```

**不支持的指令响应**:
```json
{
  "code": "SUCCESS",
  "msg": "成功", 
  "data": {
    "response": ["NOT_SUPPORTED"]
  }
}
```

---

### 3. 自然语言生成

对系统输出进行自然语言优化，使回复更加友好和自然。

**接口地址**: `POST /parse_msg`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| content | string | 是 | 需要优化的内容 |
| query | string | 否 | 原始查询语句 |

**请求示例**:

```json
{
  "content": "intent:turn_on;domain:灯;room:客厅;device:客厅主灯 执行成功",
  "query": "打开客厅的灯"
}
```

**响应示例**:

```json
{
  "code": "SUCCESS",
  "msg": "成功",
  "data": "好的，已为您打开客厅主灯"
}
```

## 错误处理

### 通用错误响应

```json
{
  "code": "ERROR",
  "msg": "错误描述信息",
  "data": null
}
```

### 常见错误类型

| 错误类型 | HTTP状态码 | 说明 |
|----------|------------|------|
| 参数验证失败 | 500 | 请求参数格式不正确 |
| 设备未找到 | 200 | 指定的设备不存在 |
| 指令不支持 | 200 | 当前指令系统无法处理 |
| 系统异常 | 500 | 服务器内部错误 |

## 指令格式说明

### 支持的意图类型

| 意图 | 说明 | 示例 |
|------|------|------|
| turn_on | 打开设备 | "打开客厅的灯" |
| turn_off | 关闭设备 | "关闭空调" |
| adjust | 调节参数 | "将亮度调到50%" |
| query | 查询状态 | "客厅灯的状态" |
| scene | 场景控制 | "打开回家模式" |

### 支持的设备域

| 域名 | 说明 | 支持参数 |
|------|------|----------|
| 灯 | 灯光设备 | 亮度、色温 |
| 空调 | 空调设备 | 温度、模式、风速 |
| 窗帘 | 窗帘设备 | 开合度 |
| 新风 | 新风设备 | 风速、模式 |
| 地暖 | 地暖设备 | 温度 |

### 输出指令格式

系统输出的控制指令采用键值对格式，用分号分隔：

```
intent:意图;domain:设备域;room:房间;device:设备名;value:参数值;scene:场景名
```

**字段说明**:
- `intent`: 控制意图（必填）
- `domain`: 设备类型域（必填）
- `room`: 房间位置（可选）
- `device`: 具体设备名（可选）
- `value`: 控制参数值（可选）
- `scene`: 场景名称（可选）

## 使用示例

### 示例1: 简单设备控制

```bash
curl -X POST http://localhost:8578/send_msg \
  -H "Content-Type: application/json" \
  -d '{
    "query": "打开客厅的灯",
    "traceId": "test-001",
    "context": {
      "rooms": ["客厅"],
      "devices": [
        {"name": "客厅主灯", "spaceName": "客厅", "intentDomainName": "灯"}
      ]
    }
  }'
```

### 示例2: 复合指令控制

```bash
curl -X POST http://localhost:8578/send_msg \
  -H "Content-Type: application/json" \
  -d '{
    "query": "打开卧室地暖并将温度调到22度",
    "traceId": "test-002", 
    "context": {
      "rooms": ["卧室"],
      "devices": [
        {"name": "卧室地暖", "spaceName": "卧室", "intentDomainName": "地暖"}
      ]
    }
  }'
```

### 示例3: 自然语言优化

```bash
curl -X POST http://localhost:8578/parse_msg \
  -H "Content-Type: application/json" \
  -d '{
    "content": "设备控制指令已执行完成",
    "query": "打开客厅的灯"
  }'
```

## 最佳实践

1. **traceId使用**: 建议使用唯一的traceId便于问题追踪
2. **设备配置**: 确保devices数组包含完整的设备信息
3. **错误处理**: 客户端应正确处理各种响应状态
4. **超时设置**: 建议设置合理的请求超时时间
5. **日志记录**: 记录关键请求和响应用于调试

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2023-12-01 | 初始版本发布 |
