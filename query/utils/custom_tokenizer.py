# utils/custom_tokenizer.py
import jieba
import re


class CustomTokenizer:
    def __init__(self):
        # 正则模式用于保护专业术语不被jieba错误切分
        #jieba可能将"制热模式"切分为"制热"+"模式"，破坏语义完整性
        self.term_pattern = re.compile(
            r'(制热模式|制冷模式|自动模式|睡眠模式|送风模式|除湿模式|制热|制冷)'
        )#
        # 使用私有区域字符作为空格占位符
        self.SPACE_PLACEHOLDER = '\uE001'

    def tokenize(self, text):
        """对输入文本进行分词处理，保护特定术语不被切分，同时保留原始空格

        这个设计采用了**"保护-分词-恢复"**的三段式策略：

        先用特殊标记__术语__包裹专业术语防止jieba错误切分，分词后再将标记替换回原始术语，确保语义完整性。
        
        参数:
            text (str): 待分词的原始文本

        返回:
            list: 分词结果列表，保留原始术语和空格
        """
        # 保留原始空格：使用特殊占位符替换空格
        text = text.replace(' ', self.SPACE_PLACEHOLDER)

        # 查找并保护专业术语
        protected_text = text
        terms = []  # 存储找到的术语
        for match in self.term_pattern.finditer(text):
            term = match.group()
            # 用特殊标记包裹术语防止被切分
            protected_text = protected_text.replace(term, f"__{term}__", 1)
            terms.append(term)

        # 使用jieba分词
        words = list(jieba.cut(protected_text))

        # 恢复被保护的专业术语
        result = []
        term_index = 0
        for word in words:
            if word.startswith("__") and word.endswith("__"):
                # 替换回原始术语
                if term_index < len(terms):
                    result.append(terms[term_index])
                    term_index += 1
            else:
                result.append(word)

        # 恢复原始空格：将占位符替换回空格
        result = [word.replace(self.SPACE_PLACEHOLDER, ' ') for word in result]

        return result


# 创建全局分词器实例
custom_tokenizer = CustomTokenizer()
