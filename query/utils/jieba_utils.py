# utils/jieba_utils.py
import jieba
from query.utils.custom_tokenizer import custom_tokenizer
# 全局变量存储初始化状态
_initialized = False
_stop_words = None
def init_jieba_once(custom_dict_path):
    global _initialized, _stop_words
    if not _initialized:
        # 加载自定义词典
        jieba.load_userdict(custom_dict_path)
        # 停用词列表
        _stop_words = {'的', '了', '呢', '啊', '请问', '这个', '那个', '一下', '请', '帮我', '我要', '能不能', '然后', '把', '为', '在'}
        _initialized = True
    return _stop_words

def get_stop_words():
    """获取已初始化的停用词集合"""
    if not _initialized:
        raise RuntimeError("Jieba not initialized. Call init_jieba_once first.")
    return _stop_words

def add_custom_dict(cus:str,freq:int):
    jieba.add_word(cus,freq = freq)

def add_device_names(device_names : list=[] ,freq:int=10):
     for device_name in device_names:
             add_custom_dict(device_name, freq)

def del_device_names(device_names : list=[]):
     for device_name in device_names:
             delete_custom_dict(device_name)
def delete_custom_dict(cus:str):
    jieba.del_word(cus)

def extract_keywords(text, stop_words):
    words = custom_tokenizer.tokenize(text)
    keywords = []
    for word in words:
        if word not in stop_words and len(word) > 0:
            keywords.append((word))
    return keywords
