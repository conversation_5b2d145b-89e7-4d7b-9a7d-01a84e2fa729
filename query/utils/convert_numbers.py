import re
import cn2an

def convert_numbers_in_text(text):
    # 正则匹配所有数字（包括整数和浮点数）
    parts = re.split(r'(\d+\.?\d*)', re.sub(r'\s+', '', text))  # 分割数字和非数字部分
    result = []
    for part in parts:
        if re.match(r'\d+\.?\d*', part):
            # 数字部分转汉字（小写模式），非数字部分保留
            hanzi_part = cn2an.an2cn(part, "low")
            result.append(hanzi_part)
        else:
            result.append(part)
    return ''.join(result)

def chinese_num_to_arabic(chinese_num: str) -> int:
        """
        支持中文数字（如"二万三千"）和混合数字（如"1.5万"）转阿拉伯数字
        """
        chinese_num = chinese_num.lower().replace(" ", "")

        # 先处理带单位的情况（如 "万", "亿"）
        unit_map = {
            'k': 1000,
            '万': 10000,
            '十万': 100000,
            '百万': 1000000,
            '千万': 10000000,
            '亿': 100000000,
            '十亿': 1000000000,
            '百亿': 10000000000,
            '千亿': 100000000000,
        }

        for unit in sorted(unit_map.keys(), key=lambda x: -len(x)):
            if unit in chinese_num:
                base = chinese_num.split(unit)[0]
                if not base:
                    continue
                try:
                    num = float(base) * unit_map[unit]
                    return int(num)
                except ValueError:
                    pass

        # 处理纯汉字数字（如“两百三十”）
        char_to_digit = {
            '零': 0, '一': 1, '二': 2, '两': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10, '百': 100, '千': 1000
        }

        result = 0
        tmp = 0
        last_unit = 1

        for ch in chinese_num:
            digit = char_to_digit.get(ch, None)
            if digit is None:
                continue

            if digit >= 10:
                if tmp == 0 and digit == 10:
                    tmp = 1
                result += tmp * digit
                tmp = 0
                last_unit = digit
            else:
                tmp = tmp * 10 + digit

        result += tmp
        return result
