class Settings:
    def __init__(self):
        self.DEVICES = []  # 设备映射
        self.SPACE = []  # 默认房间 后续会与用户传来的rooms信息同步
        self.device_names = []# 初始化为空列表，后续会与用户传来的devices信息同步
        self.SCENE_LIST = []# 初始化为空列表，后续会与用户传来的scenes信息同步
        self.connectors = []
        #下面的字典会不会有点少了？
        self.DOMAIN_LIST = ["插座","通断器","灯","开关","窗帘","空调","新风","地暖","安防"]  # 设备类型列表
        self.action_words = ['打开','开','关', '关闭', '开启', '停止', '调高', '调低', '设置', '调到', '调亮', '调暗',"暂停","设为","设成","调成","调大","调小",'关小',"开大"]