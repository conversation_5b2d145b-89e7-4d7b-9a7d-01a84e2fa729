# core/command_parser.py
import re,os,jieba
from query.core.device_manager import DeviceManager
from query.utils import jieba_utils
import jieba.posseg as pseg
from flask import current_app

#它负责将用户的自然语言指令转换为系统可以理解和执行的结构化命令
class CommandParser:
    def __init__(self, settings):
        self.settings = settings#接受queryParser.settings
        self.device_manager = DeviceManager(settings)
        # 定义动作词列表和连接词列表
        self.action_words = self.settings.action_words
        self.connectors = ["和", "以及", "然后", "同时", "并且", "还有", "接着", "跟", "与", "还有", "另外", "并", "且",
                           "并将", "将", "都", "也", "一点", "一点儿", "一些"]
        self.settings.connectors = self.connectors

        # 使用全局已初始化的停用词
        self.stop_words = jieba_utils.get_stop_words()
            
    def parse_multi_intents(self, text):
        intents = self.split_multi_intents(text)#分割复合指令
        
        # 解析每个子意图
        results = []
        # 保存前一个意图的设备信息，用于继承
        previous_devices = []
        previous_room = None
        
        
        for intent_text in intents:
            result = self.parse_single_intent(intent_text)#解析单个意图
            if result:
                if  result.get("status") == "NOT_SUPPORTED" or result.get("status")== "query_model":
                    return [result]
                # 设备继承逻辑
                if not result.get('devices') and previous_devices:
                    result['devices'] = previous_devices
                if not result.get('room') and previous_room:
                    result['room'] = previous_room
                    
                # 保存当前设备信息用于下一个意图
                if 'devices' in result and result['devices']:
                    previous_devices = result['devices']
                if 'room' in result and result['room']:
                    previous_room = result['room']
                    
                # 展平设备列表为单个设备对象
                if 'devices' in result and result['devices']:
                    for device in result['devices']:
                        new_result = {
                            "intent": result.get("intent", None),
                            "room": result.get("room", None),
                            "device": device.get("name", None),
                            "domain": result.get("domain", device.get("domain", None)),
                            "status": device.get("status", ""),
                            "scene":  result.get("scene", None),
                            "value": result.get("value",None)
                        }
                        results.append(new_result)
                else:
                    new_result = {
                        "intent": result.get("intent", None),
                        "room": result.get("room", None),
                        "device": None,
                        "domain": result.get("domain", ""),
                        "status": "",
                        "scene":  result.get("scene", None),
                        "value": result.get("value",None)
                    }
                    results.append(new_result)

        return results
    
    #用于将复合指令拆分成多个独立的子指令
    def split_multi_intents(self, text):
        # 首先进行分词
        words = list(jieba.cut(text))
        
        # 识别连接词位置
        connector_positions = []
        for i, word in enumerate(words):
            if word in self.connectors:
                connector_positions.append(i)#使用提前定义的连接词列表进行分割
        
        # 如果没有连接词，尝试基于动作词分割
        if not connector_positions:
            action_positions = []
            for i, word in enumerate(words):
                if word in self.action_words:#尝试基于定义好的动作词进行分割
                    action_positions.append(i)
            
            # 如果有多个动作词，在动作词前分割
            if len(action_positions) > 1:
                intents = []
                start = 0
                for pos in action_positions[1:]:
                    intent_text = ''.join(words[start:pos])
                    if intent_text.strip():
                        intents.append(intent_text)
                    start = pos
                if start < len(words):
                    intent_text = ''.join(words[start:])
                    if intent_text.strip():
                        intents.append(intent_text)
                return intents if intents else [text]
        
        # 基于连接词分割
        intents = []
        start_index = 0
        for pos in connector_positions:
            # 连接词前的部分作为一个意图
            intent_text = ''.join(words[start_index:pos])
            if intent_text.strip():
                intents.append(intent_text)
            
            # 更新起始位置（跳过连接词）
            start_index = pos + 1
        
        # 添加剩余部分
        if start_index < len(words):
            intent_text = ''.join(words[start_index:])
            if intent_text.strip():
                intents.append(intent_text)
        
        # 如果分割失败，尝试按语义单元分割
        if not intents:
            # 使用句法分析（简化版）识别意图边界
            # 查找动词作为意图起点
            verb_positions = []
            for i, word in enumerate(words):
                if word in self.action_words:
                    verb_positions.append(i)
            
            if verb_positions:
                intents = []
                start = 0
                for pos in verb_positions:
                    if pos > start:  # 避免空意图
                        intents.append(''.join(words[start:pos]))
                    start = pos
                if start < len(words):
                    intents.append(''.join(words[start:]))
        
        # 确保每个意图都有动作词
        if intents:
            # 从第一个意图中提取动作词
            first_action = None
            for action in self.action_words:
                if action in intents[0]:
                    first_action = action
                    break
            
            # 为后续意图添加动作词（如果需要）
            if first_action:
                for i in range(1, len(intents)):
                    has_action = any(action in intents[i] for action in self.action_words)
                    if not has_action:
                        # 在意图开头添加动作词
                        intents[i] = f"{first_action}{intents[i]}"
        
        # 房间信息继承逻辑
        if intents:
            # 提取第一个意图的房间信息
            first_room = None
            room_candidates = [r for r in self.settings.SPACE if r in intents[0]]
            if room_candidates:
                first_room = room_candidates[0]
            
            # 为后续意图添加房间信息（如果需要）
            if first_room:
                for i in range(1, len(intents)):
                    # 检查当前意图是否已有房间信息
                    has_room = any(room in intents[i] for room in self.settings.SPACE)
                    
                    # 如果没有房间信息，添加继承的房间
                    if not has_room:
                        # 在意图开头添加房间信息
                        intents[i] = f"{first_room}{intents[i]}"
        # 最终检查：如果没有分割出意图，返回整个文本
        return intents if intents else [text]
    
    def parse_single_intent(self, text):#识别单个意图
        words = jieba_utils.extract_keywords(text, self.stop_words)#提取关键词
        unsupported_devices = ["油烟机", "洗衣机", "微波炉", "电饭煲", "按摩椅", "加湿器", "投影","电视","投影机","投影仪"]
        # 求交集
        intersection = set(words) & set(unsupported_devices)
        
        # 如果有交集，说明用户提到了不支持的设备
        if intersection:
            current_app.logger.info(f"[规则引擎] 匹配失败 - 原因：检测到不支持的设备 {list(intersection)} | 切换至AI模式")
            return {
                "intent": None,
                "query": text,
                "status": "NOT_SUPPORTED",
                "unsupported_device": list(intersection)
            }
        #构建一个“场景相关关键词”的集合，为后续的意图解析（判断用户是不是在说场景）做准备。
        cmd_scene_list = []
        cmd_scene_list.extend( self.settings.SCENE_LIST)
        cmd_scene_list.extend(['场景', '执行', '启动', '运行', '开启',"走了", "出门了", "上班去了", "外出", "离家了","到家了", "回家了", "回来了","客人来了", "有客人了", "来客人了","起床了", "起床了", "醒了","睡了", "睡觉了", "休息了","出门","我要吃饭了"])
        
        # 优先判断场景
        scene_do = True #默认允许场景判断
        if any(cmd in text for cmd in self.settings.DOMAIN_LIST):#如果文本中包含具体设备域名（如"灯"、"空调"、"窗帘"）
            scene_do = False
        #第一级：场景控制（文本包含场景关键词 AND scene_do = True）
        if any(cmd in text for cmd in cmd_scene_list) and scene_do:#只有当存在场景关键词 AND 允许场景判断时，条件才成立
            current_app.logger.info(f"[规则引擎] 匹配成功 - 使用第1级规则：场景控制")
            return self.scene(text, words)
        #第二级：参数调节控制
        elif any(cmd in text for cmd in ['调高', '调低', '设置', '调到', '调亮', '调暗',"调大",'调小','关小',"开大","设置","设为",'设成','调成','调整成','调整为','调为','调整成','制热', '制冷', '自动', '睡眠',"送风","除湿"]):
            current_app.logger.info(f"[规则引擎] 匹配成功 - 使用第2级规则：参数调节控制")
            return self.set_high_or_low(text, words)
        #第三级：开关控制
        elif any(cmd in text for cmd in ['开','打开', '关','关闭', '开启', '停止',"暂停"]):
            current_app.logger.info(f"[规则引擎] 匹配成功 - 使用第3级规则：开关控制")
            return self.open_or_close(text, words)
        #第四级：未识别/需要AI处理
        else:
            current_app.logger.info(f"[规则引擎] 匹配失败 - 原因：未匹配到任何规则模式 | 切换至AI模式")
            return{
                "intent": None,
                "query": text,
                "status": "query_model",
        }
    
    def set_high_or_low(self, text, keywords):
        # 意图映射-定义了各种调节意图和对应的触发词组合。
        set_intent_map = {
            "设置亮度": [("设置", "亮度")],
            "调高亮度": [("调高", "亮度"), ("调亮","亮度"),("最亮","亮度"),("最高","亮度")],
            "调低亮度": [("调低", "亮度"), ("调暗","亮度"),("最暗","亮度"),("最低","亮度")],
            "设置色温": [("设置", "色温")],
            "调高色温": [("调高", "色温"), ("调亮", "色温"),("最高","色温"),("最大","色温")],
            "调低色温": [("调低", "色温"), ("调暗", "色温"),("最低","色温"),("最小","色温")],
            "设置开合度": [("设置", "开合度")],
            "调大开合度": [("调大", "开合度"),("开大", "开合度")],
            "调小开合度": [("调小", "开合度"),("关小", "开合度")],
            "设置温度": [("设置", "温度")],
            "调高温度": [("调高", "温度"),("最高","温度"),("最大","温度")],
            "调低温度": [("调低", "温度"),("最低","温度"),("最小","温度")],
            "调高风速": [("调高","风速"), ("调大", "风速"), ("增大", "风速")],
            "调低风速": [("调低", "风速"),("调小", "风速"), ("降低", "风速")],
            "设置风速": [("设置", "风速","自动","强","高","中","低")],
            "调高地暖": [("调高", "地暖"),("调大","地暖")],
            "调低地暖": [("调低", "地暖"),("调小","地暖")],
            "设置地暖温度": [("设置", "地暖温度")],
            "设置模式": [("设置", "模式",'制热', '制冷', '自动', '睡眠',"送风","除湿")]
        }
        
        # 提取意图
        intent = None
        value = None
        room = None
        devices = []  # 支持多设备
        
        # 检查全屋/全家
        if any(word in text for word in ['全屋', '全家']):
            room = 'all'
        elif  any(word in text for word in ['所有', '全部']):
            room = None
        
        # 提取位置
        room_candidates = [r for r in self.settings.SPACE if r in text]
        if room_candidates:
            room = room_candidates[0]
        
        # 提取设备（基于预定义列表）
        found_devices = self.device_manager.find_devices_in_text(text,room)#特殊情况 → 精确设备名 → 过滤提取
        if found_devices:
            device_name = found_devices[0].get("name")
            if "query_model" == device_name:
                current_app.logger.info(f"[规则引擎] 匹配失败 - 原因：设备识别失败，无法确定具体设备 | 切换至AI模式")
                return {
                    "intent": None,
                    "name": text,
                    "domain": None,
                    "status": "query_model"
                }
            devices = found_devices
        
        # ====================== 改进的意图匹配逻辑 ======================
        best_match_score = 0
        best_intent = None
        full_match_found = False
        
        # 1. 优先寻找完全匹配-输入的文本是否和字典中的键完全匹配
        for intent_name, trigger_combinations in set_intent_map.items():
            for triggers in trigger_combinations:
                # 检查是否完全匹配
                if all(trigger in text for trigger in triggers):
                    intent = intent_name#匹配成功-选择此意图
                    full_match_found = True
                    break  # 完全匹配，立即选择此意图

            if full_match_found:
                break

        # 2. 如果没有完全匹配，寻找最佳部分匹配
        if not full_match_found:
            for intent_name, trigger_combinations in set_intent_map.items():
                for triggers in trigger_combinations:
                    match_score = 0
                    
                    for trigger in triggers:
                        if trigger in text:
                            match_score += 1
                    
                    # 计算匹配分数（考虑触发词长度）
                    weighted_score = match_score * len(triggers)
                    
                    # 更新最佳匹配
                    if weighted_score > best_match_score:
                        best_match_score = weighted_score
                        best_intent = intent_name
            
            # 使用最佳部分匹配
            if best_intent:
                intent = best_intent
        
        # 3. 如果没有匹配，使用特殊规则
        if not intent:
            if '色温' in text:
                if '调高' in text or '调亮' in text:
                    intent = '调高色温'
                elif '调低' in text or '调暗' in text:
                    intent = '调低色温'
                elif '设置' in text:
                    intent = '设置色温'
            elif '亮度' in text:
                if '调高' in text or '调亮' in text:
                    intent = '调高亮度'
                elif '调低' in text or '调暗' in text:
                    intent = '调低亮度'
                elif '设置' in text:
                    intent = '设置亮度'
            elif '温度' in text:
                if '调高' in text:
                    intent = '调高温度'
                elif '调低' in text:
                    intent = '调低温度'
                elif '设置' in text:
                    intent = '设置温度'
            elif '风速' in text:
                if '调高' in text:
                    intent = '调高风速'
                elif '调低' in text:
                    intent = '调低风速'
                elif '设置' in text:
                    intent = '设置风速'
            elif '开合度' in text:
                if '调大' in text:
                    intent = '调大开合度'
                elif '调小' in text:
                    intent = '调小开合度'
                elif '关小' in text:
                    intent = '调小开合度'
                elif '设置' in text:
                    intent = '设置开合度'
        
        # 4. 最后尝试基于设备类型推断
        if not intent and devices:
            # 根据设备类型推断可能的意图
            device_domains = []
            for device in devices:
                if device.get("domain")!=None:
                    device_domains.append(device.get("domain"))

            
            if '灯' in device_domains:
                if '调高' in text or '调亮' in text:
                    intent = '调高亮度'
                elif '调低' in text or '调暗' in text:
                    intent = '调低亮度'
                elif '设置' in text:
                    intent = '设置亮度'
            elif '空调' in device_domains or '地暖' in device_domains:
                if '调高' in text:
                    intent = '调高温度'
                elif '调低' in text:
                    intent = '调低温度'
                elif '设置' in text:
                    intent = '设置温度'
            elif '窗帘' in device_domains:
                if '调大' in text:
                    intent = '调大开合度'
                elif '调小' in text:
                    intent = '调小开合度'
                elif '设置' in text:
                    intent = '设置开合度'
        # ====================== 结束意图匹配逻辑 ======================
        
        if intent ==None:
          return {
            "intent": intent,
            "value": value,
            "room": room,
            "devices": devices
         }
        # 提取动词
        vnouns = self.extract_vouns(text)
        action_list = ['调高', '调低', '设置', '调到', '调亮', '调暗',"调大",'调小','关小',"开大","设置","设为","设成",'调成','调为','调整成','调整为']
        # 求交集
        intersection = set(action_list) & set(vnouns)
        # 提取值
        if '设置' in intent or intersection or '%' in text or '度' in text:
            if any(word in text for word in ['最高', '最大', '最亮']):
                value = 'max'
            elif any(word in text for word in ['最低', '最小', '最暗']):
                value = 'min'
            elif '模式' in intent:
                # 提取模式名称
                mode_candidates = [word for word in keywords if word in ['制热', '制冷', '自动', '睡眠',"送风","除湿",'制热模式', '制冷模式', '自动模式', '睡眠模式',"送风模式","除湿模式","换气"]]
                if mode_candidates:
                    value = mode_candidates[0].replace("模式","").replace("换气","送风")
            elif '风速' in intent:
                # 提取模式名称
                mode_candidates = [word for word in keywords if
                                   word in ["自动","强","高","中","低","强速","高速","中速","低速","强速风","高速风","中速风","低速风","自动风","强档","高档","中档","低档"]]
                if mode_candidates:
                    value = mode_candidates[0].replace("速风","").replace("速","").replace("档","").replace("自动风","自动")
            else:
                # 提取数值 (百分比、度数等)
                old_text = text
                if devices:
                    device_names = {device['name'] for device in devices}
                    if device_names:
                        for device_name in device_names:
                            old_text = old_text.replace(device_name, "")

                # 提取数字并转换
                setting_word = intent
                for action in action_list:
                    setting_word = setting_word.replace(action,"")
                number = self.extract_number(old_text,setting_word)
                if number is not None:
                    value = str(number)
                    
        # 如果没有找到设备，但domain存在
        if not devices:
            # 尝试从文本中提取设备类型
            domain_candidates = [d for d in ['插座', '开关', '通断器', '安防', '灯', '窗帘', '空调', '新风', '地暖', '杀菌'] 
                                if d in text]
            if domain_candidates:
                domain = domain_candidates[0]
                devices.append({
                    "name": "",
                    "domain": domain,
                    "status": "domain_only"
                })
        
        return {
            "intent": intent,
            "value": value,
            "room": room,
            "devices": devices
        }

    def extract_number(self, text,setting_word):

        # 优化2：优先匹配百分比（原逻辑保留）
        setting_words = ["色温","温度","风速","亮度","开合度"]
        text = self.extract_after_keyword(setting_word+text, setting_words)
        percent_match = re.search(r'百分之([一二三四五六七八九十百千万亿]+)', text)
        if percent_match:
            chinese_num = percent_match.group(1)
            num =  self.convert_complex_chinese_num(chinese_num)  # 使用新解析函数
            return f"{num}%"

        # 2. 新增：匹配阿拉伯数字百分比 (如 50%)
        arabic_percent_match = re.search(r'(\d+\.?\d*)%', text)
        if arabic_percent_match:
            return f"{arabic_percent_match.group(1)}%"

        # 优化3：重构正则匹配，拆分单位层级
        pattern = r'([零一二两三四五六七八九十百千万亿]+)[万亿]?'  # 移除冗余符号匹配
        match = re.search(pattern, text)
        if match:
            num_str = match.group(1)
            try:
                # 使用新解析函数处理复合单位
                num = self.convert_complex_chinese_num(num_str)
                return str(num)
            except Exception as e:
                current_app.logger.error(f"extract_number error:{e}")

            # 4. 新增：匹配阿拉伯数字（整数/小数）
        arabic_percent_match = re.search(r'(\d+\.?\d*)', text)
        if arabic_percent_match:
            num_str = arabic_percent_match.group(1)
            # 处理小数
            if '.' in num_str:
                return str(float(num_str))
            # 处理整数
            return str(int(num_str))
        return None
    
    def convert_complex_chinese_num(self,num_str):
        unit_map = {'十': 10, '百': 100, '千': 1000, '万': 10000, '亿': 100000000}
        digit_map = {'零':0, '一':1, '二':2, '两':2, '三':3, '四':4, '五':5, '六':6, '七':7, '八':8, '九':9}
        result = 0
        current = 0
        last_unit_level = 0  # 记录上一个单位的层级（百/千/万）
        
        for char in num_str:
            if char in digit_map:
                current = digit_map[char]
            elif char in unit_map:
                unit_val = unit_map[char]
                # 动态补全省略的单位（如“千三” → “千三百”）
                if last_unit_level > unit_val:  # 当前单位比前一个单位小（如“千”后接“百”）
                    result += current * unit_val
                else:
                    result = (result + current) * unit_val  # 否则按层级累乘
                current = 0
                last_unit_level = unit_val  # 更新当前单位层级
        
        # 处理结尾无单位的数字（如“四千三”的“三”）
        if last_unit_level >= 100:  # 上一个单位是“百”或更大
            result += current * (last_unit_level // 10)  # 按前一级单位的1/10补全（如“千”后“三”→300）
        else:
            result += current
        
        return result
    def extract_after_keyword(self,text, keywords):
        for word in keywords:
            index = text.find(word)
            if index != -1:  # 找到关键词
                start_index = index + len(word)  # 计算截取起始位置
                return text[start_index:].strip()  # 截取并去除首尾空格
        return ""  # 无匹配关键词时返回空字符串

    def extract_vouns(self,text):
        """提取文本中的所有动词"""
        words = pseg.cut(text)
        nouns = [word for word, flag in words if flag.startswith('v')]
        return nouns

    def open_or_close(self, text, keywords):
        # 意图映射表
        intent_map = {
            "打开插座": [("插座","开"), ("插座","打开"), ("插座","开启")],
            "关闭插座": [("插座", "关"), ("插座", "关闭")],
            "打开开关": [ ("开关", "打开"), ("开关", "开启")],
            "关闭开关": [("开关", "关闭")],
            "打开灯": [("灯","开"),("灯","打开"),("灯","开启"),("开灯",)],
            "关闭灯": [("灯", "关"), ("灯", "关闭"),("关灯",)],
            "打开窗帘": [("窗帘","开"), ("窗帘", "打开"), ("窗帘", "开启")],
            "关闭窗帘": [("窗帘", "关"), ("窗帘", "关闭")],
            "暂停窗帘": [("窗帘", "暂停")],
            "打开通断器": [("通断器","开"), ("通断器", "打开"), ("通断器", "开启")],
            "关闭通断器": [("通断器", "关"), ("通断器", "关闭")],
            "打开安防": [("安防","开"), ("安防", "打开"), ("安防", "开启")],
            "关闭安防": [("安防", "关"), ("安防", "关闭")],
            "打开空调": [("空调","开"), ("空调", "打开"), ("空调", "开启")],
            "关闭空调": [("空调", "关"), ("空调", "关闭")],
            "打开新风": [("新风","开"), ("新风", "打开"), ("新风", "开启")],
            "关闭新风": [("新风", "关"), ("新风", "关闭")],
            "打开杀菌": [("杀菌","开"), ("杀菌", "打开"), ("杀菌", "开启")],
            "关闭杀菌": [("杀菌", "关"), ("杀菌", "关闭")],
            "打开地暖": [("地暖","开"), ("地暖", "打开"), ("地暖", "开启")],
            "关闭地暖": [("地暖", "关"), ("地暖", "关闭")]
        }
        
        # 提取意图
        intent = None
        domain = None
        room = None
        devices = []  # 支持多设备
        
        # 检查全屋/全家
        if any(word in text for word in ['全屋', '全家']):
            room = 'all'
        elif any(word in text for word in ['所有', '全部']):
            room = None
        
        # 提取位置
        room_candidates = [r for r in self.settings.SPACE if r in text]
        if room_candidates:
            room = room_candidates[0]
        
        # 提取设备（基于预定义列表）
      
        found_devices = self.device_manager.find_devices_in_text(text,room)
        if found_devices:
            device_name = found_devices[0].get("name")
            domain = found_devices[0].get("domain")
            if "query_model" == device_name:
                current_app.logger.info(f"[规则引擎] 匹配失败 - 原因：设备识别失败，无法确定具体设备 | 切换至AI模式")
                return {
                    "intent": None,
                    "name": text,
                    "domain": None,
                    "status": "query_model"
                }
            devices = found_devices

        # 1. 优先寻找完全匹配
        full_match_found = False
        for intent_name, trigger_combinations in intent_map.items():
            for triggers in trigger_combinations:
                # 检查是否完全匹配
                if all(trigger in text for trigger in triggers):
                    intent = intent_name
                    full_match_found = True
                    trigger_domain = triggers[0]
                    if domain and domain != trigger_domain:
                        domain = trigger_domain
                    if not domain and triggers[0]:
                        domain = triggers[0]
                    break  # 完全匹配，立即选择此意图

            if full_match_found:
                break

        # 确定意图
        # for intent_name, triggers in intent_map.items():
        #     if all(trigger in text for trigger in triggers) :
        #         intent = intent_name
        #         trigger_domain = triggers[0]
        #         if domain and domain != trigger_domain:
        #             domain = trigger_domain
        #         if not domain and triggers[0]:
        #             domain = triggers[0]
        #         break

        # 如果没有明确意图，但有关键词
        if not intent and domain:
            if '打开' in text or '开启' in text:
                intent = f"打开{domain}"
            elif '关闭' in text or '停止' in text:
                intent = f"关闭{domain}"
            elif '暂停' in text or '中断' in text:
                intent = f"暂停{domain}"
        
        # 如果没有找到设备，但domain存在
        if not devices and domain:
            devices.append({
                "name": "",
                "domain": domain,
                "status": "domain_only"
            })
        
        return {
            "intent": intent,
            "room": room,
            "domain":domain,
            "devices": devices
        }
    
    def scene(self, text, keywords):
        # 场景通用词映射
        scene_map = {
            "离家模式": ["走了", "出门了", "上班去了", "外出", "离家了","出门"],
            "回家模式": ["到家了", "回家了", "回来了"],
            "用餐模式": ["吃饭", "用餐", "就餐","我要吃饭了"],
            "会客模式": ["客人来了", "有客人了", "来客人了", "来客人", "要来客人"],
            "晨起模式": ["起床了", "起床了", "醒了"],
            "睡眠模式": ["睡了", "睡觉了", "休息了"]
        }
        
        scene = ""
        # 检查预定义场景
        for scene_name, triggers in scene_map.items():
            if any(trigger in text for trigger in triggers):
                scene = scene_name
        
        
        # 提取自定义场景名称
        # 找到动词后的部分
        action_words = ['打开', '执行', '启动', '运行', '开启']
        for word in action_words:
            if word in text:
                start_idx = text.index(word) + len(word)
                scene_name = text[start_idx:].strip()
                # 去除可能的修饰词
                for mod in ['的', '一下', '我的', '这个', '那个']:
                    if scene_name.startswith(mod):
                        scene_name = scene_name[len(mod):]
                scene = scene_name
                break
        
        # 如果没找到，使用整个文本
        if not scene:
           scene  = text
        
        res_scene = ""
        # 尝试匹配近似场景
        for scene_name in self.settings.SCENE_LIST:
            if scene_name in scene:
                 return  {
                        "scene": scene_name,
                        "domain": "场景",
                        "intent": "打开场景"
                    }
        current_app.logger.info(f"[规则引擎] 匹配失败 - 原因：场景名称未在预定义场景列表中找到 | 切换至AI模式")
        return{
                "intent": None,
                "query": text,
                "status": "query_model",
        }