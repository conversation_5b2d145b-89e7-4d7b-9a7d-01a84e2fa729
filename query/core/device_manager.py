from functools import reduce
from flask import current_app
from query.utils.custom_tokenizer import custom_tokenizer
class DeviceManager:
    def __init__(self, settings):
        self.settings = settings
        self.devices = settings.DEVICES
    
    def find_devices_in_text(self, text,room:str):
        ##特殊情况 → 精确设备名 → 过滤提取
        matched_devices = []
        # 优先先匹配品类
        new_device_name = self.extract_nouns(text)#提取设备名称
        # 名词未提取成功，则直接走模型查询
        if new_device_name == "query_model":
                matched_devices.append({
                        "name": new_device_name,
                        "domain": "",
                        "status": "device not found"
                })
        else:
            if new_device_name in self.settings.DOMAIN_LIST:#如果提取的名称在域名列表中，直接匹配设备类型：
                    matched_devices.append({
                        "name": "",
                        "domain": new_device_name,
                        "status": "found"
            })
            ## 根据提取的设备名称匹配
            if not matched_devices:
                for device in self.settings.DEVICES:
                    device_name = device.get("name")
                    domain = device.get("domain")
                    space_name = None
                    if room:
                      space_name = device.get("space_name")

                    if new_device_name == device_name and (space_name is None or space_name == room or room == "all"):
                        matched_devices.append({
                            "name": device_name,
                            "domain": domain,
                            "room": room,
                            "status": "found"
                        })
        matched_devices = self.deduplicate_by_keys(matched_devices, keys=['name', 'domain'])
        # 如果没有找到精确匹配，尝试设备类型匹配
        if not matched_devices:
              matched_devices.append({
                        "name": "",
                        "query": text,
                        "status": "DEVICE_NOT_FOUND"
                })
   
        return matched_devices
    
    def get_domain_devices(self, domain):
        """获取特定类型的所有设备"""
        return [name for name, d_domain in self.devices.items() if d_domain == domain]
    
    def extract_nouns(self, text: str):#特殊情况 → 精确设备名 → 过滤提取

        #特殊情况
        if "开灯" == text or "关灯" == text:
            return "灯"#对于常见的"开灯"/"关灯"指令，直接返回"灯"

        #精确设备名：先分词，判断是否已存在设备名称
        words = custom_tokenizer.tokenize(text)#使用自定义jieba分词器对文本分词
        #如果设备名称在分词结果中，且不是通用域名（如"灯"、"空调"），直接返回该设备名称
        for device in self.settings.DEVICES:
            device_name = device.get("name")
            domain = device.get("domain")
            if device_name in words:
                if device_name not in self.settings.DOMAIN_LIST:
                    return device_name
            
        """提取文本中的所有名词"""
      #  new_words = pseg.cut(text)
       # nouns = [word for word, flag in new_words if flag.startswith('n')]

        #过滤提取：过滤词列表构建
        filter_list = []
        if self.settings.SPACE:
            filter_list.extend(self.settings.SPACE)
        filter_list.extend(["全家", "全屋", "所有", "全部","色温","温度","风速","模式","亮度","开合度","的", "模式",'制热', '制冷', '自动', '睡眠',"送风","除湿","为","设","点","点儿","成",'制热模式', '制冷模式', '自动模式', '睡眠模式',"送风模式","除湿模式","最亮","最暗","最大","最小","最高","最低","自动","强","高","中","低","强速","高速","中速","低速","强档","高档","中档","低档","调为","调整为","调整成","强速风","高速风","中速风","低速风","自动风"])
        filter_list.extend(self.settings.action_words)
        filter_list.extend(self.settings.connectors)

        nouns = list(filter(lambda x: x not in filter_list, words))

        if nouns:
            merged_string = reduce(lambda x, y: x + y, nouns)
            current_app.logger.info(f"merged_string: {merged_string}")
            return merged_string
        else:
            return "query_model"
            
    
    def deduplicate_by_keys(self,data, keys):
        seen = set()
        result = []
        for item in data:
            # 生成去重依据的元组（如 ('灯', '开关')）
            identifier = tuple(item[key] for key in keys)
            if identifier not in seen:
                result.append(item)
                seen.add(identifier)
        return result

       
       
        