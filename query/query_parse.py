from query.config import settings
from query.core.command_parser import CommandParser
from entity.config import Config
from query.utils import jieba_utils
from mode.hw.dialogue_parser import parse_dialogue
from flask import current_app
import json,os


# 在服务启动时初始化 Jieba
def initialize_jieba():
    # 计算自定义词典路径（原 CommandParser 中的逻辑）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir,"..", "query", "data")
    custom_dict_path = os.path.normpath(os.path.join(data_dir, "custom_dict.txt"))

    # 只初始化一次
    jieba_utils.init_jieba_once(custom_dict_path)


# 在应用启动时调用初始化
initialize_jieba()

class queryParser:
    def __init__(self):
        self.settings = settings.Settings()
        self.parser = CommandParser(self.settings)
    #负责将用户的自然语言转换为可执行的设备控制指令。
    def parse_command(self,text:str,config:Config,traceId:str):

        #首先解析多轮对话，提取最后一条用户消息作为当前要处理的指令。
        parsed_list = parse_dialogue(text)
        if parsed_list:
            text = parsed_list[len(parsed_list) - 1].get("content")
        
        #从配置中提取所有设备信息，构建设备列表
        device_names = []
        for k in config.devices:#设备集合中
            name = k.get("name")#设备总名称
            space_name = k.get("spaceName")#设备名称
            domain = k.get("intentDomainName")#设备类别
           # name = convert_numbers_in_text(name)
            self.settings.DEVICES.append({"name": name, "domain": domain,"space_name":space_name})
            device_names.append(name)

        #将设备信息同步到解析器的配置中，确保解析时能识别所有可用设备和场景。    
        config.device_names = device_names
        self.config = config
        #把用户传过去的config信息同步到settings中
        self.settings.SPACE = config.space
        self.settings.SCENE_LIST=config.SCENE_LIST
        self.settings.device_names = device_names
        current_app.logger.info(f"traceId:{traceId},请求参数:  self.settings.DEVICES"+json.dumps(self.settings.DEVICES, indent=2, ensure_ascii=False))

        device_names.extend(["地暖","制热","空调","制冷"])#添加通用设备词汇
        if "打开灯" in text:
            device_names.extend("打开")
        jieba_utils.add_device_names(device_names, 10)#将所有设备名称以高权重(10)临时添加到jieba词典中，确保分词时优先识别这些词汇。
        results = self.parser.parse_multi_intents(text)#使用解析器进行多意图解析
        jieba_utils.del_device_names(device_names)#删除临时添加的词典
        return results
        

