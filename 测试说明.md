# 智能家居语音指令测试工具

## 概述

本工具包含了完整的智能家居语音指令测试数据和测试脚本，用于验证API的功能和性能。

## 文件说明

### 1. `api_post.py` - 原始API测试脚本
- 包含完整的设备和房间配置
- 适用于单个查询的详细测试
- 包含完整的错误处理和响应分析

### 2. `test_queries.py` - 批量测试脚本
- 包含所有测试查询数据（400+条）
- 支持批量测试和分类测试
- 提供详细的统计分析

### 3. `quick_test.py` - 快速测试脚本
- 简化的测试流程
- 适用于快速验证单个查询
- 包含增强的设备配置

## 测试数据覆盖范围

### 天气查询 (40+条)
- 地理位置查询：北京、深圳、顺德、佛山、广东等
- 时间维度查询：今天、明天、后天、未来一周等
- 详细信息查询：温度、湿度、空气质量、风速等
- 生活指数查询：化妆、紫外线、感冒、洗车等指数

### 灯光控制 (80+条)
- 基础控制：开启/关闭各种灯具
- 亮度调节：调高/调低/设置具体数值
- 色温调节：调高/调低/设置具体色温值
- 设备类型：筒灯、明装筒灯、吸顶灯、氛围灯等
- 空间范围：主卧、全屋等

### 窗帘控制 (40+条)
- 基础控制：开启/关闭/暂停
- 窗帘类型：纱帘、布帘、窗帘电机、窗帘面板
- 开合度设置：设置具体百分比
- 空间范围：主卧、全屋等

### 空调控制 (50+条)
- 基础控制：开启/关闭
- 温度控制：调高/调低/设置具体温度
- 模式控制：制冷/制热/送风/换气/除湿
- 风速控制：高速/中速/低速/自动
- 设备范围：主卧空调、客厅空调、全家空调等

### 其他设备控制 (100+条)
- 开关控制：各种开关面板
- 插座控制：智能插座开关
- 地暖控制：地暖开关
- 新风控制：新风机控制和风速调节
- 厨房设备：油烟机、电饭煲、微波炉等
- 其他设备：洗衣机、按摩椅、加湿器、投影仪等

### 情景模式 (20+条)
- 生活场景：回家模式、离家模式、睡眠模式、用餐模式等
- 时间场景：晨起模式
- 功能场景：全开模式、全关模式

### 组合控制 (30+条)
- 多设备组合：灯光+空调、灯光+地暖等
- 多参数组合：亮度+色温同时调节
- 复杂指令：多步骤操作组合

## 使用方法

### 1. 快速测试单个查询
```bash
# 使用默认查询测试
python quick_test.py

# 测试自定义查询
python quick_test.py "打开主卧的灯"
python quick_test.py "深圳今天的天气怎么样"
```

### 2. 批量测试
```bash
# 测试所有查询（400+条）
python test_queries.py all

# 测试天气相关查询
python test_queries.py weather

# 测试灯光相关查询  
python test_queries.py light

# 测试窗帘相关查询
python test_queries.py curtain

# 测试空调相关查询
python test_queries.py ac

# 测试情景模式查询
python test_queries.py scene

# 测试单个查询
python test_queries.py "打开主卧的灯"
```

### 3. 原始API测试
```bash
# 运行原始测试脚本
python api_post.py
```

## 请求头配置特点

### 丰富的房间配置
- 基础房间：客厅、主卧、次卧、书房、餐厅、厨房等
- 特殊空间：过道、全屋、海上（用于测试边界情况）

### 完整的设备配置
- **灯具设备**：筒灯、明装筒灯、吸顶灯、氛围灯等
- **控制设备**：各种开关、通断器、面板等
- **窗帘设备**：纱帘、布帘、窗帘电机、窗帘面板等
- **空调设备**：主卧空调、客厅空调、全家空调等
- **其他设备**：插座、地暖、新风机、厨房设备等

### 情景模式支持
- 生活场景：会客、离家、睡眠、用餐、回家、晨起
- 功能场景：全开、全关、测试专用

### 地理位置信息
- 位置：广东省佛山市顺德区北滘镇
- 城市代码：440600
- 经纬度：113.20699596198394, 22.928231957573892

## 测试结果分析

测试脚本会提供以下信息：
- **请求耗时**：HTTP请求总时间
- **模型推理时间**：AI模型处理时间
- **响应状态**：成功/失败状态
- **模型响应**：AI模型的具体回复
- **错误信息**：失败时的详细错误信息

## 注意事项

1. **服务地址**：默认使用 `http://127.0.0.1:8578`，请确保服务正常运行
2. **请求频率**：批量测试时会在请求间添加1秒延迟，避免服务器压力过大
3. **超时设置**：单个请求超时时间为30秒
4. **错误处理**：包含完整的网络错误、JSON解析错误等处理

## 扩展使用

### 添加新的测试查询
在 `test_queries.py` 的 `TEST_QUERIES` 列表中添加新的查询字符串。

### 修改设备配置
在 `get_request_payload()` 或 `get_enhanced_payload()` 函数中修改 `devices` 列表。

### 自定义测试分类
在 `test_queries.py` 中添加新的测试分类函数，参考现有的分类测试方法。

## 性能基准

基于测试数据，可以建立以下性能基准：
- **响应时间**：正常情况下应在2-5秒内响应
- **成功率**：正常情况下成功率应达到95%以上
- **推理时间**：模型推理时间应在1-3秒内

这些测试工具可以帮助你全面验证智能家居语音助手的功能完整性和性能表现。
