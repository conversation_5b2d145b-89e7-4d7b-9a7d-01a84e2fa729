#!/usr/bin/env python3
"""
Qwen2-1.5B-Instruct智能家居控制模型对比脚本
对比原始模型与微调后模型的性能表现
"""

import json
import time
import os
import pandas as pd
import torch
from datetime import datetime
from typing import Dict, List, Tuple, Any
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import matplotlib.pyplot as plt
from collections import defaultdict
import re

# 指定使用5号GPU
os.environ["CUDA_VISIBLE_DEVICES"] = "6"

# 修复transformers库的ALL_PARALLEL_STYLES问题
from transformers import modeling_utils
if not hasattr(modeling_utils, "ALL_PARALLEL_STYLES") or modeling_utils.ALL_PARALLEL_STYLES is None:
    modeling_utils.ALL_PARALLEL_STYLES = ["tp", "none", "colwise", "rowwise"]

class ModelComparator:
    def __init__(self):
        """初始化模型对比器"""
        self.base_model_path = "/opt/LLM_MODEL/Qwen2-1.5B-Instruct/qwen/Qwen2-1.5B-Instruct/"
        self.finetuned_model_path = "./output/Qwen1.5/checkpoint-100/"
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # 统一的系统提示词
        self.system_prompt = """你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

1. 所有输出必须是一个**标准 JSON 对象**，且包含名为 `"function_calls"` 的字段，其值为一个 JSON 数组。

2. 设备控制强制规则：
   1. **room 参数必须精确匹配**：
      - 只返回用户明确提到的位置词（如"客厅"、"卧室"）
      - 未提及位置时返回"默认"

   2. **禁止位置推断**：
      - 示例：用户说"打开床头灯"
        - 正确：room = "默认"
        - 错误：room = "卧室"（禁止推断）

   3. **全屋规则**：
      - 仅当用户明确说"全屋"、"所有设备"时才返回"all"

3. 每个数组元素是一个合法的函数调用对象，结构如下：
   {
     "name": "函数名",
     "arguments": {
       "intent": "意图，来自预定义枚举列表[\"打开插座\", \"关闭插座\",\"打开开关\",\"关闭开关\",\"打开灯\",\"关闭灯\",\"打开窗帘\",\"关闭窗帘\",\"暂停窗帘\",\"打开通断器\",\"关闭通断器\",\"打开安防\",\"关闭安防\",\"打开空调\",\"关闭空调\",\"打开新风\",\"关闭新风\",\"打开杀菌\",\"关闭杀菌\",\"打开地暖\",\"关闭地暖\",\"设置亮度\",\"调高亮度\",\"调低亮度\",\"设置色温\",\"调高色温\",\"调低色温\",\"设置开合度\",\"调大开合度\",\"调小开合度\",\"设置温度\",\"调高温度\",\"调低温度\",\"设置风速\",\"调高风速\",\"调低风速\",\"调高地暖\",\"调低地暖\",\"设置地暖温度\",\"打开场景\",\"查询限行\",\"查询化妆指数\",\"查询紫外线指数\",\"查询感冒指数\",\"查询洗车指数\",\"查询穿衣指数\",\"查询运动指数\",\"查询钓鱼指数\",\"闲聊\",\"设备数量查询\",\"终止对话\",\"重新开始\",\"敏感词\",\"自我介绍\",\"查询空气质量&空气污染扩散指数\",\"查询空气湿度\",\"查询温度/体感温度\",\"查询风速/风向\",\"查询天气状况\",\"查询日出/日落时间\"]",
       "content": "回答用户问题，用于闲聊对话，严格按照纯文本输出",
       "domain": "意图域，来自预定义枚举列表[\"插座\",\"通断器\",\"灯\",\"开关\",\"窗帘\",\"空调\",\"新风\",\"地暖\",\"场景\",\"天气\",\"生活指数\",\"闲聊\",\"对话\",\"\"]；若未指定，默认为'默认'",
       "value": "设置值（仅 setHighOrLow 函数需要）",
       "room": "空间位置，若用户提到\"全屋\"、\"全部\"、\"所有\"，返回 \"all\"；未明确则默认为 \"默认\"",
       "device": "设备昵称，用于描述用户指定的设备昵称，若设备昵称和domain参数值一致，则返回空字符串",
       "scene": "场景名称，如离家/回家模式，开灯/关灯，打开/关闭全屋灯",
       "pos": "查询天气或生活指数的地点，默认为伦敦",
       "offset": "查询时间偏移量规则，若为具体某一天，必须带符号：今天 → '+0', 明天 → '+1', 后天 → '+2', 昨天 → '-1'",
       "unit": "查询天气或者生活指数的时间单位，默认为day，来自预定义枚举列表[\"pos\", \"year\", \"month\", \"day\", \"week\", \"hour\", \"minute\", \"second\",\"timeRange\"]"
     }
   }

4. 必须从以下函数中选择合适的进行调用：
   - openOrClose：用于开关类操作
   - setHighOrLow：用于调节亮度、色温、风速、温度、开合度等
   - scene：用于执行场景（如回家模式）
   - getWeather：查询天气信息
   - getLiving：查询生活指数
   - queryDevice：查询设备数量或状态
   - chat：处理闲聊类对话
   - dialog：处理对话意图（终止、重新开始、敏感词）
   - xiaoling：自我介绍

5. 参数要求：
   - intent 必须从预定义枚举列表中选择，不能随意构造
   - domain 必须匹配函数支持的设备类型
   - value 只能是数字或数字+百分号（%），不允许带单位文字
   - device 若和 domain 相同则返回空字符串，否则返回具体设备昵称

6. 输出必须是纯 JSON，不带任何解释、注释、Markdown 或多余字段。

7. 多个意图需生成多个 function_call，按语义顺序排列。

示例输入："帮我把卧室的灯调高亮度，再打开客厅的窗帘，并启动回家模式"
示例输出：
{
  "function_calls": [
    {
      "name": "setHighOrLow",
      "arguments": {
        "intent": "调高亮度",
        "domain": "灯",
        "value": "",
        "room": "卧室",
        "device": ""
      }
    },
    {
      "name": "openOrClose",
      "arguments": {
        "intent": "打开窗帘",
        "domain": "窗帘",
        "room": "客厅",
        "device": ""
      }
    },
    {
      "name": "scene",
      "arguments": {
        "intent": "打开场景",
        "domain": "场景",
        "scene": "回家模式"
      }
    }
  ]
}"""

        print("🚀 正在初始化模型对比器...")
        self.load_models()
        self.load_test_data()
        
    def load_models(self):
        """加载原始模型和微调模型"""
        try:
            print("📥 正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.base_model_path, 
                use_fast=False, 
                trust_remote_code=True
            )
            
            print("📥 正在加载原始基础模型...")
            self.base_model = AutoModelForCausalLM.from_pretrained(
                self.base_model_path,
                torch_dtype=torch.bfloat16,
                trust_remote_code=True,
                device_map="auto"
            )
            
            print("📥 正在加载微调模型...")
            # 先加载基础模型，然后加载LoRA权重
            base_for_finetuned = AutoModelForCausalLM.from_pretrained(
                self.base_model_path,
                torch_dtype=torch.bfloat16,
                trust_remote_code=True,
                device_map="auto"
            )
            self.finetuned_model = PeftModel.from_pretrained(
                base_for_finetuned, 
                self.finetuned_model_path
            )
            
            print("✅ 模型加载完成！")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 加载验证集数据
            if os.path.exists("val_split.jsonl"):
                self.test_data = pd.read_json("val_split.jsonl", lines=True)
                print(f"📊 加载测试数据: {len(self.test_data)} 条")
            else:
                print("⚠️  未找到验证集数据，将使用预定义测试用例")
                self.test_data = self.get_predefined_test_cases()
                
        except Exception as e:
            print(f"⚠️  加载测试数据失败: {e}，使用预定义测试用例")
            self.test_data = self.get_predefined_test_cases()
    
    def get_predefined_test_cases(self):
        """获取预定义的测试用例"""
        test_cases = [
            {
                "instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释",
                "input": "关闭客厅灯",
                "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭灯\", \"domain\": \"灯\", \"room\": \"客厅\", \"device\": \"\"}}]}"
            },
            {
                "instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释",
                "input": "调节卧室灯光亮度到80%",
                "output": "{\"function_calls\": [{\"name\": \"setHighOrLow\", \"arguments\": {\"intent\": \"设置亮度\", \"domain\": \"灯\", \"room\": \"卧室\", \"device\": \"卧室吸顶灯\", \"value\": \"80\"}}]}"
            },
            {
                "instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释",
                "input": "关闭书房空调，然后启动睡眠模式",
                "output": "{\"function_calls\": [{\"name\": \"openOrClose\", \"arguments\": {\"intent\": \"关闭空调\", \"domain\": \"空调\", \"room\": \"书房\", \"device\": \"\"}}, {\"name\": \"scene\", \"arguments\": {\"intent\": \"打开场景\", \"domain\": \"场景\", \"scene\": \"睡眠模式\"}}]}"
            },
            {
                "instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释",
                "input": "你好",
                "output": "{\"function_calls\": [{\"name\": \"chat\", \"arguments\": {\"intent\": \"闲聊\", \"domain\": \"闲聊\", \"content\": \"您好！很高兴为您服务，请问需要控制哪些设备呢？\"}}]}"
            },
            {
                "instruction": "你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：\n\n1. 输出必须是标准JSON对象，包含\"function_calls\"字段，值为JSON数组\n2. 设备控制强制规则：\n   - room参数必须精确匹配用户明确提到的位置词\n   - 未提及位置时返回\"默认\"\n   - 禁止位置推断（如\"床头灯\"不能推断为\"卧室\"）\n   - 仅当用户明确说\"全屋\"、\"所有设备\"时才返回\"all\"\n3. 支持的函数：openOrClose（开关控制）、setHighOrLow（调节控制）、scene（场景控制）、getWeather（天气查询）、getLiving（生活指数查询）、queryDevice（设备查询）、chat（闲聊对话）、dialog（对话控制）、xiaoling（自我介绍）\n4. intent必须从预定义枚举列表中选择，domain必须匹配设备类型\n5. 输出纯JSON格式，不带解释或注释",
                "input": "查询天气",
                "output": "{\"function_calls\": [{\"name\": \"getWeather\", \"arguments\": {\"intent\": \"查询天气状况\", \"domain\": \"天气\", \"pos\": \"伦敦\", \"offset\": \"+0\", \"unit\": \"day\"}}]}"
            }
        ]
        return pd.DataFrame(test_cases)
    
    def predict(self, model, messages: List[Dict], model_name: str) -> Tuple[str, float]:
        """使用指定模型进行预测"""
        try:
            start_time = time.time()
            
            # 构建输入文本
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )
            
            # 编码输入
            model_inputs = self.tokenizer([text], return_tensors="pt").to(self.device)
            
            # 生成响应
            with torch.no_grad():
                generated_ids = model.generate(
                    model_inputs.input_ids,
                    max_new_tokens=512,
                    do_sample=False,
                    temperature=0.1,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码响应
            generated_ids = [
                output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
            ]
            
            response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
            
            inference_time = time.time() - start_time
            
            return response.strip(), inference_time
            
        except Exception as e:
            print(f"❌ {model_name}预测失败: {e}")
            return f"ERROR: {str(e)}", 0.0

    def evaluate_json_format(self, response: str) -> bool:
        """评估JSON格式正确性"""
        try:
            json.loads(response)
            return True
        except:
            return False

    def evaluate_function_calls(self, response: str) -> Dict[str, Any]:
        """评估function_calls结构正确性"""
        result = {
            "has_function_calls": False,
            "function_calls_count": 0,
            "valid_structure": False,
            "functions": []
        }

        try:
            data = json.loads(response)
            if "function_calls" in data and isinstance(data["function_calls"], list):
                result["has_function_calls"] = True
                result["function_calls_count"] = len(data["function_calls"])
                result["valid_structure"] = True

                for func_call in data["function_calls"]:
                    if isinstance(func_call, dict) and "name" in func_call and "arguments" in func_call:
                        result["functions"].append(func_call["name"])
                    else:
                        result["valid_structure"] = False
                        break
        except:
            pass

        return result

    def evaluate_parameters(self, response: str, expected_output: str = None) -> Dict[str, Any]:
        """评估参数提取精度"""
        result = {
            "has_intent": False,
            "has_domain": False,
            "has_room": False,
            "parameter_accuracy": 0.0,
            "extracted_params": {}
        }

        try:
            data = json.loads(response)
            if "function_calls" in data:
                all_params = []
                for func_call in data["function_calls"]:
                    if "arguments" in func_call:
                        args = func_call["arguments"]
                        if "intent" in args:
                            result["has_intent"] = True
                        if "domain" in args:
                            result["has_domain"] = True
                        if "room" in args:
                            result["has_room"] = True
                        all_params.append(args)

                result["extracted_params"] = all_params

                # 计算参数完整性得分
                param_score = 0
                if result["has_intent"]:
                    param_score += 1
                if result["has_domain"]:
                    param_score += 1
                if result["has_room"]:
                    param_score += 1

                result["parameter_accuracy"] = param_score / 3.0

        except:
            pass

        return result

    def compare_models_interactive(self):
        """交互式模型对比"""
        print("\n🎯 交互式模型对比模式")
        print("输入智能家居控制指令，输入 'quit' 退出")
        print("=" * 60)

        while True:
            user_input = input("\n请输入指令: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                break

            if not user_input:
                continue

            # 构建消息
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_input}
            ]

            print(f"\n📝 用户输入: {user_input}")
            print("-" * 60)

            # 原始模型预测
            print("🔵 原始模型输出:")
            base_response, base_time = self.predict(self.base_model, messages, "原始模型")
            print(f"响应时间: {base_time:.3f}s")
            print(f"输出: {base_response}")

            # 评估原始模型
            base_json_valid = self.evaluate_json_format(base_response)
            base_func_eval = self.evaluate_function_calls(base_response)
            base_param_eval = self.evaluate_parameters(base_response)

            print(f"JSON格式: {'✅' if base_json_valid else '❌'}")
            print(f"Function调用: {'✅' if base_func_eval['valid_structure'] else '❌'} ({base_func_eval['function_calls_count']}个)")
            print(f"参数完整性: {base_param_eval['parameter_accuracy']:.1%}")

            print("\n🟢 微调模型输出:")
            finetuned_response, finetuned_time = self.predict(self.finetuned_model, messages, "微调模型")
            print(f"响应时间: {finetuned_time:.3f}s")
            print(f"输出: {finetuned_response}")

            # 评估微调模型
            finetuned_json_valid = self.evaluate_json_format(finetuned_response)
            finetuned_func_eval = self.evaluate_function_calls(finetuned_response)
            finetuned_param_eval = self.evaluate_parameters(finetuned_response)

            print(f"JSON格式: {'✅' if finetuned_json_valid else '❌'}")
            print(f"Function调用: {'✅' if finetuned_func_eval['valid_structure'] else '❌'} ({finetuned_func_eval['function_calls_count']}个)")
            print(f"参数完整性: {finetuned_param_eval['parameter_accuracy']:.1%}")

            # 对比总结
            print(f"\n📊 对比总结:")
            print(f"响应速度: 微调模型 {'🚀' if finetuned_time < base_time else '🐌'} ({finetuned_time/base_time:.2f}x)")
            print(f"JSON格式: 微调模型 {'🏆' if finetuned_json_valid and not base_json_valid else '🤝' if finetuned_json_valid == base_json_valid else '📉'}")
            print(f"参数提取: 微调模型 {'🏆' if finetuned_param_eval['parameter_accuracy'] > base_param_eval['parameter_accuracy'] else '🤝' if finetuned_param_eval['parameter_accuracy'] == base_param_eval['parameter_accuracy'] else '📉'}")

            print("=" * 60)

    def run_batch_evaluation(self, num_samples: int = None) -> Dict[str, Any]:
        """运行批量自动化评估"""
        print(f"\n🔄 开始批量评估...")

        # 选择测试样本
        if num_samples is None:
            test_samples = self.test_data
        else:
            test_samples = self.test_data.head(num_samples)

        print(f"📊 测试样本数量: {len(test_samples)}")

        results = {
            "base_model": {
                "responses": [],
                "times": [],
                "json_valid": [],
                "function_valid": [],
                "parameter_scores": [],
                "errors": []
            },
            "finetuned_model": {
                "responses": [],
                "times": [],
                "json_valid": [],
                "function_valid": [],
                "parameter_scores": [],
                "errors": []
            },
            "test_cases": []
        }

        for idx, row in test_samples.iterrows():
            print(f"🧪 测试 {idx+1}/{len(test_samples)}: {row['input'][:50]}...")

            # 构建消息 - 使用统一的系统提示词
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": row['input']}
            ]

            # 测试原始模型
            base_response, base_time = self.predict(self.base_model, messages, "原始模型")
            base_json_valid = self.evaluate_json_format(base_response)
            base_func_eval = self.evaluate_function_calls(base_response)
            base_param_eval = self.evaluate_parameters(base_response, row.get('output'))

            results["base_model"]["responses"].append(base_response)
            results["base_model"]["times"].append(base_time)
            results["base_model"]["json_valid"].append(base_json_valid)
            results["base_model"]["function_valid"].append(base_func_eval["valid_structure"])
            results["base_model"]["parameter_scores"].append(base_param_eval["parameter_accuracy"])
            results["base_model"]["errors"].append("ERROR" in base_response)

            # 测试微调模型
            finetuned_response, finetuned_time = self.predict(self.finetuned_model, messages, "微调模型")
            finetuned_json_valid = self.evaluate_json_format(finetuned_response)
            finetuned_func_eval = self.evaluate_function_calls(finetuned_response)
            finetuned_param_eval = self.evaluate_parameters(finetuned_response, row.get('output'))

            results["finetuned_model"]["responses"].append(finetuned_response)
            results["finetuned_model"]["times"].append(finetuned_time)
            results["finetuned_model"]["json_valid"].append(finetuned_json_valid)
            results["finetuned_model"]["function_valid"].append(finetuned_func_eval["valid_structure"])
            results["finetuned_model"]["parameter_scores"].append(finetuned_param_eval["parameter_accuracy"])
            results["finetuned_model"]["errors"].append("ERROR" in finetuned_response)

            # 保存测试用例
            results["test_cases"].append({
                "input": row['input'],
                "expected_output": row.get('output', ''),
                "base_response": base_response,
                "finetuned_response": finetuned_response,
                "base_time": base_time,
                "finetuned_time": finetuned_time
            })

        return results

    def generate_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成详细的对比报告"""
        print("\n📋 生成对比报告...")

        # 计算统计指标
        base_stats = {
            "json_success_rate": sum(results["base_model"]["json_valid"]) / len(results["base_model"]["json_valid"]),
            "function_success_rate": sum(results["base_model"]["function_valid"]) / len(results["base_model"]["function_valid"]),
            "avg_parameter_score": sum(results["base_model"]["parameter_scores"]) / len(results["base_model"]["parameter_scores"]),
            "avg_response_time": sum(results["base_model"]["times"]) / len(results["base_model"]["times"]),
            "error_rate": sum(results["base_model"]["errors"]) / len(results["base_model"]["errors"])
        }

        finetuned_stats = {
            "json_success_rate": sum(results["finetuned_model"]["json_valid"]) / len(results["finetuned_model"]["json_valid"]),
            "function_success_rate": sum(results["finetuned_model"]["function_valid"]) / len(results["finetuned_model"]["function_valid"]),
            "avg_parameter_score": sum(results["finetuned_model"]["parameter_scores"]) / len(results["finetuned_model"]["parameter_scores"]),
            "avg_response_time": sum(results["finetuned_model"]["times"]) / len(results["finetuned_model"]["times"]),
            "error_rate": sum(results["finetuned_model"]["errors"]) / len(results["finetuned_model"]["errors"])
        }

        # 计算改进指标
        improvements = {
            "json_improvement": finetuned_stats["json_success_rate"] - base_stats["json_success_rate"],
            "function_improvement": finetuned_stats["function_success_rate"] - base_stats["function_success_rate"],
            "parameter_improvement": finetuned_stats["avg_parameter_score"] - base_stats["avg_parameter_score"],
            "speed_improvement": (base_stats["avg_response_time"] - finetuned_stats["avg_response_time"]) / base_stats["avg_response_time"],
            "error_reduction": base_stats["error_rate"] - finetuned_stats["error_rate"]
        }

        report = {
            "timestamp": datetime.now().isoformat(),
            "test_samples": len(results["test_cases"]),
            "base_model_stats": base_stats,
            "finetuned_model_stats": finetuned_stats,
            "improvements": improvements,
            "detailed_results": results
        }

        return report

    def save_report(self, report: Dict[str, Any], filename: str = None):
        """保存报告到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"model_comparison_report_{timestamp}"

        # 保存JSON格式
        json_filename = f"{filename}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        # 保存可读文本格式
        txt_filename = f"{filename}.txt"
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write("🤖 Qwen2-1.5B-Instruct 智能家居控制模型对比报告\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"📅 测试时间: {report['timestamp']}\n")
            f.write(f"📊 测试样本数: {report['test_samples']}\n\n")

            f.write("📈 性能指标对比:\n")
            f.write("-" * 40 + "\n")
            f.write(f"{'指标':<20} {'原始模型':<15} {'微调模型':<15} {'改进':<15}\n")
            f.write("-" * 40 + "\n")

            base_stats = report['base_model_stats']
            finetuned_stats = report['finetuned_model_stats']
            improvements = report['improvements']

            f.write(f"{'JSON格式正确率':<20} {base_stats['json_success_rate']:<15.1%} {finetuned_stats['json_success_rate']:<15.1%} {improvements['json_improvement']:+.1%}\n")
            f.write(f"{'Function调用正确率':<20} {base_stats['function_success_rate']:<15.1%} {finetuned_stats['function_success_rate']:<15.1%} {improvements['function_improvement']:+.1%}\n")
            f.write(f"{'参数提取准确率':<20} {base_stats['avg_parameter_score']:<15.1%} {finetuned_stats['avg_parameter_score']:<15.1%} {improvements['parameter_improvement']:+.1%}\n")
            f.write(f"{'平均响应时间(s)':<20} {base_stats['avg_response_time']:<15.3f} {finetuned_stats['avg_response_time']:<15.3f} {improvements['speed_improvement']:+.1%}\n")
            f.write(f"{'错误率':<20} {base_stats['error_rate']:<15.1%} {finetuned_stats['error_rate']:<15.1%} {improvements['error_reduction']:+.1%}\n")

        print(f"✅ 报告已保存:")
        print(f"   📄 JSON格式: {json_filename}")
        print(f"   📝 文本格式: {txt_filename}")

    def create_visualizations(self, report: Dict[str, Any], filename: str = None):
        """创建可视化图表"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"model_comparison_charts_{timestamp}.png"

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Qwen2-1.5B-Instruct 模型对比分析', fontsize=16, fontweight='bold')

        base_stats = report['base_model_stats']
        finetuned_stats = report['finetuned_model_stats']

        # 1. 成功率对比
        metrics = ['JSON格式', 'Function调用', '参数提取']
        base_scores = [base_stats['json_success_rate'], base_stats['function_success_rate'], base_stats['avg_parameter_score']]
        finetuned_scores = [finetuned_stats['json_success_rate'], finetuned_stats['function_success_rate'], finetuned_stats['avg_parameter_score']]

        x = range(len(metrics))
        width = 0.35

        axes[0, 0].bar([i - width/2 for i in x], base_scores, width, label='原始模型', alpha=0.8)
        axes[0, 0].bar([i + width/2 for i in x], finetuned_scores, width, label='微调模型', alpha=0.8)
        axes[0, 0].set_xlabel('评估指标')
        axes[0, 0].set_ylabel('成功率')
        axes[0, 0].set_title('模型性能对比')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(metrics)
        axes[0, 0].legend()
        axes[0, 0].set_ylim(0, 1.1)

        # 2. 响应时间对比
        time_data = [base_stats['avg_response_time'], finetuned_stats['avg_response_time']]
        axes[0, 1].bar(['原始模型', '微调模型'], time_data, color=['skyblue', 'lightgreen'], alpha=0.8)
        axes[0, 1].set_ylabel('响应时间 (秒)')
        axes[0, 1].set_title('平均响应时间对比')

        # 3. 错误率对比
        error_data = [base_stats['error_rate'], finetuned_stats['error_rate']]
        axes[1, 0].bar(['原始模型', '微调模型'], error_data, color=['salmon', 'lightcoral'], alpha=0.8)
        axes[1, 0].set_ylabel('错误率')
        axes[1, 0].set_title('错误率对比')

        # 4. 改进幅度
        improvements = report['improvements']
        improvement_metrics = ['JSON格式', 'Function调用', '参数提取', '响应速度', '错误减少']
        improvement_values = [
            improvements['json_improvement'],
            improvements['function_improvement'],
            improvements['parameter_improvement'],
            improvements['speed_improvement'],
            improvements['error_reduction']
        ]

        colors = ['green' if x > 0 else 'red' for x in improvement_values]
        axes[1, 1].bar(improvement_metrics, improvement_values, color=colors, alpha=0.8)
        axes[1, 1].set_ylabel('改进幅度')
        axes[1, 1].set_title('微调模型相对改进')
        axes[1, 1].tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 可视化图表已保存: {filename}")

        return filename


def main():
    """主函数"""
    print("🤖 Qwen2-1.5B-Instruct 智能家居控制模型对比工具")
    print("=" * 60)

    try:
        # 初始化对比器
        comparator = ModelComparator()

        while True:
            print("\n📋 请选择功能:")
            print("1. 交互式对比测试")
            print("2. 批量自动化评估 (5个样本)")
            print("3. 批量自动化评估 (所有样本)")
            print("4. 退出")

            choice = input("\n请输入选择 (1-4): ").strip()

            if choice == "1":
                comparator.compare_models_interactive()

            elif choice == "2":
                print("\n🔄 开始批量评估 (5个样本)...")
                results = comparator.run_batch_evaluation(num_samples=5)
                report = comparator.generate_report(results)

                # 显示简要结果
                print("\n📊 评估结果摘要:")
                print("-" * 40)
                base_stats = report['base_model_stats']
                finetuned_stats = report['finetuned_model_stats']
                improvements = report['improvements']

                print(f"JSON格式正确率: 原始 {base_stats['json_success_rate']:.1%} → 微调 {finetuned_stats['json_success_rate']:.1%} ({improvements['json_improvement']:+.1%})")
                print(f"Function调用正确率: 原始 {base_stats['function_success_rate']:.1%} → 微调 {finetuned_stats['function_success_rate']:.1%} ({improvements['function_improvement']:+.1%})")
                print(f"参数提取准确率: 原始 {base_stats['avg_parameter_score']:.1%} → 微调 {finetuned_stats['avg_parameter_score']:.1%} ({improvements['parameter_improvement']:+.1%})")
                print(f"平均响应时间: 原始 {base_stats['avg_response_time']:.3f}s → 微调 {finetuned_stats['avg_response_time']:.3f}s ({improvements['speed_improvement']:+.1%})")

                # 保存报告
                comparator.save_report(report)
                comparator.create_visualizations(report)

            elif choice == "3":
                print("\n🔄 开始完整批量评估...")
                results = comparator.run_batch_evaluation()
                report = comparator.generate_report(results)

                # 显示详细结果
                print("\n📊 完整评估结果:")
                print("-" * 50)
                base_stats = report['base_model_stats']
                finetuned_stats = report['finetuned_model_stats']
                improvements = report['improvements']

                print(f"测试样本数: {report['test_samples']}")
                print(f"JSON格式正确率: 原始 {base_stats['json_success_rate']:.1%} → 微调 {finetuned_stats['json_success_rate']:.1%} ({improvements['json_improvement']:+.1%})")
                print(f"Function调用正确率: 原始 {base_stats['function_success_rate']:.1%} → 微调 {finetuned_stats['function_success_rate']:.1%} ({improvements['function_improvement']:+.1%})")
                print(f"参数提取准确率: 原始 {base_stats['avg_parameter_score']:.1%} → 微调 {finetuned_stats['avg_parameter_score']:.1%} ({improvements['parameter_improvement']:+.1%})")
                print(f"平均响应时间: 原始 {base_stats['avg_response_time']:.3f}s → 微调 {finetuned_stats['avg_response_time']:.3f}s ({improvements['speed_improvement']:+.1%})")
                print(f"错误率: 原始 {base_stats['error_rate']:.1%} → 微调 {finetuned_stats['error_rate']:.1%} ({improvements['error_reduction']:+.1%})")

                # 保存报告和图表
                comparator.save_report(report)
                comparator.create_visualizations(report)

            elif choice == "4":
                print("👋 感谢使用模型对比工具！")
                break

            else:
                print("❌ 无效选择，请重新输入")

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
